/**
 * 系统管理 - 系统日志页面
 * 高级现代化日志管理系统
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';
import LogFileList from '../../../components/logs/LogFileList.js';
import LogViewer from '../../../components/logs/LogViewer.js';
import LogSearch from '../../../components/logs/LogSearch.js';
import LogFilters from '../../../components/logs/LogFilters.js';

createStandardApp({
    components: {
        Sidebar,
        LogFileList,
        LogViewer,
        LogSearch,
        LogFilters
    },
    setup() {
        const { ref, reactive, computed, onMounted, onUnmounted } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        // 日志管理状态
        const logState = reactive({
            selectedFile: null,
            searchTerm: '',
            levelFilter: '',
            timeFilter: null,
            searchResults: [],
            files: [],
            isSearchMode: false,
            statistics: {
                totalFiles: 0,
                totalSize: 0,
                errorCount: 0,
                warnCount: 0,
                infoCount: 0
            }
        });

        // 计算属性
        const hasSelectedFile = computed(() => !!logState.selectedFile);
        const isFiltered = computed(() => logState.levelFilter || logState.timeFilter);

        // 处理文件选择
        const handleFileSelected = (file) => {
            logState.selectedFile = file;
            logState.isSearchMode = false;
            logState.searchResults = [];

            if (file) {
                console.log(`选择日志文件: ${file.name}`);
            }
        };

        // 处理搜索词变化
        const handleSearchTermChange = (term) => {
            logState.searchTerm = term;
            logState.isSearchMode = !!term.trim();
        };

        // 处理搜索结果
        const handleSearchResults = (results) => {
            logState.searchResults = results;
            logState.isSearchMode = true;
        };

        // 处理级别过滤变化
        const handleLevelFilterChange = (level) => {
            logState.levelFilter = level;
        };

        // 处理时间过滤变化
        const handleTimeFilterChange = (timeFilter) => {
            logState.timeFilter = timeFilter;
        };

        // 处理过滤器重置
        const handleFiltersReset = () => {
            logState.levelFilter = '';
            logState.timeFilter = null;
            logState.searchTerm = '';
            logState.isSearchMode = false;
            logState.searchResults = [];
        };

        // 处理文件列表更新
        const handleFilesUpdated = (files) => {
            logState.files = files;
            updateStatistics(files);
        };

        // 更新统计信息
        const updateStatistics = (files) => {
            logState.statistics.totalFiles = files.length;
            logState.statistics.totalSize = files.reduce((total, file) => total + file.size, 0);

            // 这里可以添加更多统计逻辑，比如统计各级别日志数量
            // 由于需要读取文件内容，这里暂时设置为0
            logState.statistics.errorCount = 0;
            logState.statistics.warnCount = 0;
            logState.statistics.infoCount = 0;
        };

        // 格式化文件大小
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        // 快捷键处理
        const handleKeydown = (event) => {
            // Ctrl+F 聚焦搜索框
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                const searchInput = document.querySelector('input[type="text"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Esc 清除搜索
            if (event.key === 'Escape') {
                handleFiltersReset();
            }
        };

        // 组件挂载时设置事件监听
        onMounted(() => {
            document.addEventListener('keydown', handleKeydown);
            console.log('系统日志页面初始化完成');
        });

        // 组件卸载时清理事件监听
        onUnmounted(() => {
            document.removeEventListener('keydown', handleKeydown);
        });

        return {
            sidebarOpen,
            logState,
            hasSelectedFile,
            isFiltered,
            handleFileSelected,
            handleSearchTermChange,
            handleSearchResults,
            handleLevelFilterChange,
            handleTimeFilterChange,
            handleFiltersReset,
            handleFilesUpdated,
            formatFileSize
        };
    },
    onUserLoaded: async (user) => {
        // 手动检查用户角色
        if (user.role !== 'admin') {
            // 显示权限不足页面
            document.getElementById('app').innerHTML = `
                <div class="flex items-center justify-center h-screen bg-gray-100">
                    <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
                        <svg class="mx-auto h-16 w-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">权限不足</h2>
                        <p class="text-gray-600 mb-6">您没有访问系统日志的权限，此功能仅限管理员使用。</p>
                        <a href="/dashboard" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            返回主页
                        </a>
                    </div>
                </div>
            `;
            return;
        }
    }
}).mount('#app');
