/**
 * 库存事务控制器
 * 处理库存事务相关的HTTP请求
 */

const TransactionsService = require('../services/transactionsService');
const logger = require('../../../utils/logger');

class TransactionsController {
    /**
     * 获取库存事务列表
     */
    async getTransactions(req, res) {
        try {
            const filters = {
                transaction_type: req.query.transaction_type,
                item_type: req.query.item_type,
                item_id: req.query.item_id,
                operator_id: req.query.operator_id,
                start_date: req.query.start_date,
                end_date: req.query.end_date,
                qrcode: req.query.qrcode,
                limit: req.query.limit
            };

            const transactions = await TransactionsService.getTransactions(filters);
            
            res.json({
                success: true,
                data: transactions,
                message: '获取库存事务列表成功'
            });
        } catch (error) {
            logger.error('获取库存事务列表失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取库存事务列表失败'
            });
        }
    }

    /**
     * 根据ID获取库存事务详情
     */
    async getTransactionById(req, res) {
        try {
            const { id } = req.params;
            const transaction = await TransactionsService.getTransactionById(id);
            
            res.json({
                success: true,
                data: transaction,
                message: '获取库存事务详情成功'
            });
        } catch (error) {
            logger.error('获取库存事务详情失败:', error);
            res.status(error.message === '库存事务不存在' ? 404 : 500).json({
                success: false,
                message: error.message || '获取库存事务详情失败'
            });
        }
    }

    /**
     * 根据物品获取库存事务
     */
    async getTransactionsByItem(req, res) {
        try {
            const { itemType, itemId } = req.params;
            const filters = {
                transaction_type: req.query.transaction_type,
                start_date: req.query.start_date,
                end_date: req.query.end_date,
                limit: req.query.limit
            };

            const transactions = await TransactionsService.getTransactionsByItem(itemType, itemId, filters);
            
            res.json({
                success: true,
                data: transactions,
                message: '获取物品库存事务成功'
            });
        } catch (error) {
            logger.error('获取物品库存事务失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取物品库存事务失败'
            });
        }
    }

    /**
     * 获取库存事务统计
     */
    async getTransactionStats(req, res) {
        try {
            const filters = {
                start_date: req.query.start_date,
                end_date: req.query.end_date,
                item_type: req.query.item_type
            };

            const stats = await TransactionsService.getTransactionStats(filters);
            
            res.json({
                success: true,
                data: stats,
                message: '获取库存事务统计成功'
            });
        } catch (error) {
            logger.error('获取库存事务统计失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取库存事务统计失败'
            });
        }
    }

    /**
     * 获取物品当前库存
     */
    async getCurrentStock(req, res) {
        try {
            const { itemType, itemId } = req.params;
            const stock = await TransactionsService.getCurrentStock(itemType, itemId);
            
            res.json({
                success: true,
                data: { 
                    item_type: itemType,
                    item_id: itemId,
                    current_stock: stock 
                },
                message: '获取当前库存成功'
            });
        } catch (error) {
            logger.error('获取当前库存失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取当前库存失败'
            });
        }
    }

    /**
     * 获取批次库存
     */
    async getBatchStock(req, res) {
        try {
            const { itemType, itemId, batchNumber } = req.params;
            const stock = await TransactionsService.getBatchStock(itemType, itemId, batchNumber);
            
            res.json({
                success: true,
                data: { 
                    item_type: itemType,
                    item_id: itemId,
                    batch_number: batchNumber,
                    batch_stock: stock 
                },
                message: '获取批次库存成功'
            });
        } catch (error) {
            logger.error('获取批次库存失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取批次库存失败'
            });
        }
    }
}

module.exports = new TransactionsController();
