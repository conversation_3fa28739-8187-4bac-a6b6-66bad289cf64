/**
 * 日志管理系统专用样式
 * 现代化的日志查看器界面样式
 * 路径：frontend/assets/css/logs-management.css
 */

/* 现代化日志管理容器 */
.logs-container-modern {
    min-height: calc(100vh - 200px);
}

/* 现代化控制面板 */
.control-panel-modern {
    animation: slideInFromTop 0.6s ease-out;
}

/* 现代化查看器面板 */
.viewer-panel-modern {
    animation: slideInFromBottom 0.6s ease-out;
    min-height: 600px;
}

/* 传统布局兼容 */
.logs-container {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.logs-control-panel {
    display: grid;
    grid-template-columns: 1fr 300px 250px;
    gap: 1rem;
    height: 400px;
}

.logs-viewer-panel {
    flex: 1;
    min-height: 0;
}

/* 现代化日志文件列表样式 */
.log-file-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
    border-radius: 0.75rem;
    margin-bottom: 0.5rem;
    backdrop-filter: blur(10px);
}

.log-file-item:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-left-color: #3b82f6;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.log-file-item.selected {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-left-color: #2563eb;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
    transform: translateX(6px);
}

.log-file-item.selected:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.35);
}

/* 现代化日志级别徽章样式 */
.log-level-badge {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 700;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-size: 0.7rem;
    min-width: 70px;
    text-align: center;
    border-radius: 9999px;
    padding: 0.375rem 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.log-level-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.log-level-badge:hover::before {
    left: 100%;
}

.log-level-error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 2px solid #f87171;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.25);
}

.log-level-warn {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
    color: #d97706;
    border: 2px solid #fbbf24;
    box-shadow: 0 4px 12px rgba(217, 119, 6, 0.25);
}

.log-level-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #2563eb;
    border: 2px solid #60a5fa;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.log-level-debug {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #6b7280;
    border: 2px solid #9ca3af;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.25);
}

.log-level-http {
    background: linear-gradient(135deg, #d1fae5 0%, #bbf7d0 100%);
    color: #16a34a;
    border: 2px solid #4ade80;
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.25);
}

/* 日志内容样式 */
.log-content {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    word-break: break-all;
    white-space: pre-wrap;
}

.log-entry {
    transition: background-color 0.15s ease;
    border-left: 3px solid transparent;
}

.log-entry:hover {
    background-color: #f8fafc;
    border-left-color: #e2e8f0;
}

.log-entry.error {
    border-left-color: #ef4444;
}

.log-entry.warn {
    border-left-color: #f59e0b;
}

.log-entry.info {
    border-left-color: #3b82f6;
}

.log-entry.debug {
    border-left-color: #6b7280;
}

.log-entry.http {
    border-left-color: #10b981;
}

/* 现代化搜索高亮样式 */
.search-highlight {
    background: linear-gradient(135deg, #fef08a 0%, #fde047 100%);
    color: #92400e;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
    animation: highlightPulse 2s ease-in-out infinite;
    border: 1px solid #fbbf24;
}

@keyframes highlightPulse {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
    }
    50% {
        box-shadow: 0 4px 16px rgba(251, 191, 36, 0.6);
    }
}

/* 时间戳样式 */
.log-timestamp {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
}

/* 虚拟滚动容器 */
.virtual-scroll-container {
    height: 100%;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.virtual-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 加载动画 */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 脉冲动画 */
.pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 搜索框样式增强 */
.search-input-container {
    position: relative;
}

.search-input-container input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 过滤器按钮样式 */
.filter-button {
    transition: all 0.2s ease;
    position: relative;
}

.filter-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.filter-button.active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 日志统计卡片 */
.log-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.log-stats-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.log-stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .logs-control-panel {
        grid-template-columns: 1fr;
        height: auto;
        gap: 1rem;
    }
    
    .logs-container {
        height: calc(100vh - 80px);
    }
}

@media (max-width: 768px) {
    .logs-control-panel {
        grid-template-columns: 1fr;
    }
    
    .log-content {
        font-size: 0.75rem;
    }
    
    .log-level-badge {
        font-size: 0.625rem;
        min-width: 50px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .log-entry:hover {
        background-color: #1f2937;
    }
    
    .virtual-scroll-container::-webkit-scrollbar-track {
        background: #374151;
    }
    
    .virtual-scroll-container::-webkit-scrollbar-thumb {
        background: #6b7280;
    }
    
    .virtual-scroll-container::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 工具提示样式 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 现代化状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.75rem;
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    animation: statusRipple 2s ease-out infinite;
}

.status-indicator.online {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.status-indicator.online::after {
    border: 2px solid #10b981;
}

.status-indicator.offline {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.status-indicator.offline::after {
    border: 2px solid #ef4444;
}

.status-indicator.loading {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    animation: statusPulse 1.5s ease-in-out infinite;
}

.status-indicator.loading::after {
    border: 2px solid #f59e0b;
    animation: statusRipple 1s ease-out infinite;
}

@keyframes statusRipple {
    0% {
        opacity: 1;
        transform: scale(0);
    }
    100% {
        opacity: 0;
        transform: scale(1);
    }
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* 现代化页面动画 */
@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 现代化卡片样式 */
.modern-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 现代化按钮样式 */
.modern-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.025em;
}

.modern-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.modern-button:hover::before {
    left: 100%;
}

.modern-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 现代化输入框样式 */
.modern-input {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.75rem;
    border: 2px solid #e5e7eb;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.modern-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

/* 现代化加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
    border-radius: 0.5rem;
}

@keyframes skeletonLoading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 现代化滚动条 */
.modern-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

/* 现代化工具提示 */
.modern-tooltip {
    position: relative;
}

.modern-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.modern-tooltip:hover::after {
    opacity: 1;
    transform: translateX(-50%) translateY(-4px);
}
