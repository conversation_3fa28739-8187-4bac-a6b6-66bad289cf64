<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料成品管理 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/warehouse/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            仓库管理 - 物料成品管理
                        </span>
                    </li>
                </ol>
            </nav>

            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-6">物料成品管理</h2>

                <!-- 类型切换选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeItemType = 'material'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeItemType === 'material'}">
                            物料管理
                        </button>
                        <button
                            @click="activeItemType = 'finished_product'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeItemType === 'finished_product'}">
                            成品管理
                        </button>
                    </div>
                </div>

                <!-- 操作选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeOperation = 'list'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-green-600 transition-colors duration-200"
                            :class="{'border-b-2 border-green-500 text-green-600': activeOperation === 'list'}">
                            <i class="fas fa-list mr-1"></i>
                            列表管理
                        </button>
                        <button
                            @click="activeOperation = 'inbound'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeOperation === 'inbound'}">
                            <i class="fas fa-arrow-down mr-1"></i>
                            入库操作
                        </button>
                        <button
                            @click="activeOperation = 'outbound'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-orange-600 transition-colors duration-200"
                            :class="{'border-b-2 border-orange-500 text-orange-600': activeOperation === 'outbound'}">
                            <i class="fas fa-arrow-up mr-1"></i>
                            {{ activeItemType === 'material' ? '发料操作' : '出库操作' }}
                        </button>
                        <button
                            @click="activeOperation = 'return'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-purple-600 transition-colors duration-200"
                            :class="{'border-b-2 border-purple-500 text-purple-600': activeOperation === 'return'}">
                            <i class="fas fa-undo mr-1"></i>
                            {{ activeItemType === 'material' ? '退料操作' : '返仓操作' }}
                        </button>
                    </div>
                </div>

                <!-- 列表管理 -->
                <div v-if="activeOperation === 'list'" class="space-y-6">
                    <!-- 搜索和筛选 -->
                    <div class="filter-section">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label class="filter-label">搜索</label>
                                <div class="relative w-64">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <input type="text" v-model="searchTerm" placeholder="搜索编号、名称..."
                                           class="pl-10 pr-8 py-2 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <button v-if="searchTerm" @click="searchTerm = ''" class="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="filter-group">
                                <label class="filter-label">库存状态</label>
                                <select v-model="stockFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">全部状态</option>
                                    <option value="normal">正常</option>
                                    <option value="low">偏低</option>
                                    <option value="critical">紧急</option>
                                    <option value="out">缺货</option>
                                </select>
                            </div>

                            <div class="action-buttons">
                                <button @click="openAddModal" class="action-btn primary">
                                    <i class="fas fa-plus"></i>
                                    添加{{ activeItemType === 'material' ? '物料' : '成品' }}
                                </button>
                                <button @click="refreshList" class="action-btn secondary">
                                    <i class="fas fa-sync-alt"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div v-if="isLoading" class="warehouse-loading">
                        <div class="warehouse-spinner"></div>
                        <span class="ml-2 text-gray-600">加载中...</span>
                    </div>

                    <div v-else-if="filteredItems.length === 0" class="py-8 text-center text-gray-500">
                        没有找到符合条件的{{ activeItemType === 'material' ? '物料' : '成品' }}
                    </div>

                    <div v-else class="overflow-x-auto">
                        <table class="warehouse-table">
                            <thead>
                                <tr>
                                    <th>编号</th>
                                    <th>名称</th>
                                    <th>规格</th>
                                    <th>单位</th>
                                    <th>当前库存</th>
                                    <th>库存状态</th>
                                    <th>最小库存</th>
                                    <th>最大库存</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in paginatedItems" :key="item.id">
                                    <td class="font-mono">{{ item.code }}</td>
                                    <td class="font-medium">{{ item.name }}</td>
                                    <td>{{ item.specification || '-' }}</td>
                                    <td>{{ item.unit }}</td>
                                    <td class="font-semibold">{{ item.current_stock || 0 }}</td>
                                    <td>
                                        <span class="stock-indicator" :class="getStockStatusClass(item)"></span>
                                        {{ getStockStatusText(item) }}
                                    </td>
                                    <td>{{ item.min_stock || '-' }}</td>
                                    <td>{{ item.max_stock || '-' }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button @click="editItem(item)" class="action-btn primary" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button @click="deleteItem(item)" class="action-btn danger" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div v-if="totalPages > 1" class="flex justify-between items-center mt-6">
                        <div class="text-sm text-gray-500">
                            共 {{ totalItems }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
                        </div>
                        <div class="flex space-x-2">
                            <button @click="goToPage(currentPage - 1)" :disabled="currentPage <= 1" 
                                    class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                上一页
                            </button>
                            <button @click="goToPage(currentPage + 1)" :disabled="currentPage >= totalPages"
                                    class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                下一页
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 入库操作 -->
                <div v-if="activeOperation === 'inbound'" class="space-y-6">
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-arrow-down text-blue-500 mr-2"></i>
                            {{ activeItemType === 'material' ? '物料入库' : '成品入库' }}
                        </h3>
                        
                        <!-- 二维码扫描区域 -->
                        <div class="qr-scan-area mb-6" :class="{'active': isScanning}">
                            <div class="text-center">
                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">扫描二维码或手动输入</p>
                                <input type="text" v-model="qrCodeInput" @keyup.enter="validateQRCode" 
                                       placeholder="请扫描或输入二维码"
                                       class="w-full max-w-md mx-auto px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button @click="validateQRCode" class="mt-2 action-btn primary">
                                    <i class="fas fa-search"></i>
                                    验证二维码
                                </button>
                            </div>
                        </div>

                        <!-- 入库表单 -->
                        <div v-if="selectedItem" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold mb-3">{{ activeItemType === 'material' ? '物料' : '成品' }}信息</h4>
                                <div class="space-y-2 text-sm">
                                    <div><span class="font-medium">编号：</span>{{ selectedItem.code }}</div>
                                    <div><span class="font-medium">名称：</span>{{ selectedItem.name }}</div>
                                    <div><span class="font-medium">规格：</span>{{ selectedItem.specification || '-' }}</div>
                                    <div><span class="font-medium">单位：</span>{{ selectedItem.unit }}</div>
                                    <div><span class="font-medium">当前库存：</span>{{ selectedItem.current_stock || 0 }}</div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-3">入库信息</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">入库数量 <span class="text-red-500">*</span></label>
                                        <div class="quantity-controls">
                                            <button @click="decreaseQuantity" class="quantity-btn">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" v-model.number="operationForm.quantity" min="1" 
                                                   class="quantity-input px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <button @click="increaseQuantity" class="quantity-btn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                        <textarea v-model="operationForm.notes" rows="3" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                  placeholder="请输入入库备注信息"></textarea>
                                    </div>
                                    
                                    <div class="action-buttons">
                                        <button @click="submitInbound" :disabled="isSubmitting" class="action-btn success">
                                            <i class="fas fa-check"></i>
                                            {{ isSubmitting ? '处理中...' : '确认入库' }}
                                        </button>
                                        <button @click="clearOperation" class="action-btn secondary">
                                            <i class="fas fa-times"></i>
                                            清除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 出库操作 -->
                <div v-if="activeOperation === 'outbound'" class="space-y-6">
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-arrow-up text-orange-500 mr-2"></i>
                            {{ activeItemType === 'material' ? '物料发料' : '成品出库' }}
                        </h3>
                        
                        <!-- 二维码扫描区域 -->
                        <div class="qr-scan-area mb-6" :class="{'active': isScanning}">
                            <div class="text-center">
                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">扫描二维码或手动输入</p>
                                <input type="text" v-model="qrCodeInput" @keyup.enter="validateQRCode" 
                                       placeholder="请扫描或输入二维码"
                                       class="w-full max-w-md mx-auto px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button @click="validateQRCode" class="mt-2 action-btn primary">
                                    <i class="fas fa-search"></i>
                                    验证二维码
                                </button>
                            </div>
                        </div>

                        <!-- 出库表单 -->
                        <div v-if="selectedItem" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold mb-3">{{ activeItemType === 'material' ? '物料' : '成品' }}信息</h4>
                                <div class="space-y-2 text-sm">
                                    <div><span class="font-medium">编号：</span>{{ selectedItem.code }}</div>
                                    <div><span class="font-medium">名称：</span>{{ selectedItem.name }}</div>
                                    <div><span class="font-medium">规格：</span>{{ selectedItem.specification || '-' }}</div>
                                    <div><span class="font-medium">单位：</span>{{ selectedItem.unit }}</div>
                                    <div><span class="font-medium">当前库存：</span>
                                        <span :class="{'text-red-500': selectedItem.current_stock <= 0, 'text-orange-500': selectedItem.current_stock <= selectedItem.min_stock}">
                                            {{ selectedItem.current_stock || 0 }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-3">{{ activeItemType === 'material' ? '发料' : '出库' }}信息</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ activeItemType === 'material' ? '发料' : '出库' }}数量 <span class="text-red-500">*</span></label>
                                        <div class="quantity-controls">
                                            <button @click="decreaseQuantity" class="quantity-btn">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" v-model.number="operationForm.quantity" min="1" :max="selectedItem.current_stock"
                                                   class="quantity-input px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <button @click="increaseQuantity" class="quantity-btn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <p v-if="operationForm.quantity > selectedItem.current_stock" class="text-xs text-red-500 mt-1">
                                            数量不能超过当前库存
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ activeItemType === 'material' ? '领料' : '收货' }}部门</label>
                                        <input type="text" v-model="operationForm.department" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="请输入部门名称">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                        <textarea v-model="operationForm.notes" rows="3" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                  placeholder="请输入备注信息"></textarea>
                                    </div>
                                    
                                    <div class="action-buttons">
                                        <button @click="submitOutbound" :disabled="isSubmitting || operationForm.quantity > selectedItem.current_stock" class="action-btn warning">
                                            <i class="fas fa-check"></i>
                                            {{ isSubmitting ? '处理中...' : `确认${activeItemType === 'material' ? '发料' : '出库'}` }}
                                        </button>
                                        <button @click="clearOperation" class="action-btn secondary">
                                            <i class="fas fa-times"></i>
                                            清除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 退料/返仓操作 -->
                <div v-if="activeOperation === 'return'" class="space-y-6">
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-undo text-purple-500 mr-2"></i>
                            {{ activeItemType === 'material' ? '物料退料' : '成品返仓' }}
                        </h3>
                        
                        <!-- 二维码扫描区域 -->
                        <div class="qr-scan-area mb-6" :class="{'active': isScanning}">
                            <div class="text-center">
                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">扫描二维码或手动输入</p>
                                <input type="text" v-model="qrCodeInput" @keyup.enter="validateQRCode" 
                                       placeholder="请扫描或输入二维码"
                                       class="w-full max-w-md mx-auto px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button @click="validateQRCode" class="mt-2 action-btn primary">
                                    <i class="fas fa-search"></i>
                                    验证二维码
                                </button>
                            </div>
                        </div>

                        <!-- 退料/返仓表单 -->
                        <div v-if="selectedItem" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold mb-3">{{ activeItemType === 'material' ? '物料' : '成品' }}信息</h4>
                                <div class="space-y-2 text-sm">
                                    <div><span class="font-medium">编号：</span>{{ selectedItem.code }}</div>
                                    <div><span class="font-medium">名称：</span>{{ selectedItem.name }}</div>
                                    <div><span class="font-medium">规格：</span>{{ selectedItem.specification || '-' }}</div>
                                    <div><span class="font-medium">单位：</span>{{ selectedItem.unit }}</div>
                                    <div><span class="font-medium">当前库存：</span>{{ selectedItem.current_stock || 0 }}</div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold mb-3">{{ activeItemType === 'material' ? '退料' : '返仓' }}信息</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ activeItemType === 'material' ? '退料' : '返仓' }}数量 <span class="text-red-500">*</span></label>
                                        <div class="quantity-controls">
                                            <button @click="decreaseQuantity" class="quantity-btn">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <input type="number" v-model.number="operationForm.quantity" min="1" 
                                                   class="quantity-input px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <button @click="increaseQuantity" class="quantity-btn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ activeItemType === 'material' ? '退料' : '返仓' }}原因</label>
                                        <select v-model="operationForm.reason" 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">请选择原因</option>
                                            <option value="quality_issue">质量问题</option>
                                            <option value="excess_material">多余物料</option>
                                            <option value="wrong_specification">规格错误</option>
                                            <option value="production_change">生产变更</option>
                                            <option value="other">其他原因</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                                        <textarea v-model="operationForm.notes" rows="3" 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                  placeholder="请输入详细说明"></textarea>
                                    </div>
                                    
                                    <div class="action-buttons">
                                        <button @click="submitReturn" :disabled="isSubmitting" class="action-btn primary" style="background: #8B5CF6;">
                                            <i class="fas fa-check"></i>
                                            {{ isSubmitting ? '处理中...' : `确认${activeItemType === 'material' ? '退料' : '返仓'}` }}
                                        </button>
                                        <button @click="clearOperation" class="action-btn secondary">
                                            <i class="fas fa-times"></i>
                                            清除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加/编辑模态框 -->
        <div v-if="showItemModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg p-4 md:p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4 sticky top-0 bg-white pt-1">
                    <h3 class="text-lg font-semibold">{{ isEditing ? '编辑' : '添加' }}{{ activeItemType === 'material' ? '物料' : '成品' }}</h3>
                    <button @click="closeItemModal" class="text-gray-500 hover:text-gray-700 p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form @submit.prevent="submitItemForm" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">编号 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="itemForm.code" required placeholder="例如：M001"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">名称 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="itemForm.name" required placeholder="请输入名称"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规格</label>
                            <input type="text" v-model="itemForm.specification" placeholder="请输入规格"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="itemForm.unit" required placeholder="例如：个、kg、米"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">最小库存</label>
                            <input type="number" v-model.number="itemForm.min_stock" min="0" placeholder="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">最大库存</label>
                            <input type="number" v-model.number="itemForm.max_stock" min="0" placeholder="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                        <textarea v-model="itemForm.description" rows="3" placeholder="请输入描述信息"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" @click="closeItemModal" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none">
                            取消
                        </button>
                        <button type="submit" :disabled="isSubmitting" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none disabled:opacity-50">
                            {{ isSubmitting ? '提交中...' : (isEditing ? '更新' : '创建') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/warehouse/materials-products.js"></script>
</body>
</html>
