{"name": "makrite-application-system", "version": "1.0.0", "description": "管理系统", "scripts": {"start": "cd backend && set NODE_ENV=production && npm start", "start:backend": "cd backend && set NODE_ENV=production && npm start", "start:frontend": "cd frontend && npx serve -s", "start:dual": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "install:all": "npm install && cd backend && npm install", "dev": "cd backend && set NODE_ENV=development && npm run dev", "dev:backend": "cd backend && set NODE_ENV=development && npm run dev", "dev:frontend": "cd frontend && npx serve -s", "dev:dual": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\""}, "dependencies": {"@tailwindcss/cli": "^4.1.11", "axios": "^1.6.0", "concurrently": "^8.0.1", "cross-env": "^10.0.0", "node-fetch": "^2.7.0", "serve": "^14.2.0", "sharp": "^0.34.3", "tailwindcss": "^4.1.11"}}