/**
 * 安全增强中间件
 * 提供速率限制、输入验证、安全头等功能
 */

const logger = require('../utils/logger');

// 尝试加载可选依赖
let rateLimit, helmet, validator;
try {
    rateLimit = require('express-rate-limit');
} catch (e) {
    console.warn('express-rate-limit 未安装，速率限制功能将被禁用');
}

try {
    helmet = require('helmet');
} catch (e) {
    console.warn('helmet 未安装，安全头功能将被禁用');
}

try {
    validator = require('validator');
} catch (e) {
    console.warn('validator 未安装，将使用简化的输入验证');
}

/**
 * 创建速率限制中间件
 */
function createRateLimitMiddleware(options = {}) {
    if (!rateLimit) {
        // 如果没有安装 express-rate-limit，返回一个空中间件
        return (req, res, next) => next();
    }

    const {
        windowMs = 15 * 60 * 1000, // 15分钟
        max = 100, // 最大请求数
        message = '请求过于频繁，请稍后再试',
        skipSuccessfulRequests = false,
        skipFailedRequests = false
    } = options;

    return rateLimit({
        windowMs,
        max,
        message: {
            success: false,
            message,
            retryAfter: Math.ceil(windowMs / 1000)
        },
        standardHeaders: true,
        legacyHeaders: false,
        skipSuccessfulRequests,
        skipFailedRequests,
        handler: (req, res) => {
            // 只在生产环境记录详细的速率限制日志
            if (process.env.NODE_ENV === 'production') {
                logger.warn('速率限制触发', {
                    ip: req.ip,
                    userAgent: req.headers['user-agent'],
                    url: req.originalUrl,
                    method: req.method
                });
            } else {
                logger.debug('速率限制触发 (开发环境)', {
                    url: req.originalUrl,
                    method: req.method
                });
            }

            res.status(429).json({
                success: false,
                message,
                retryAfter: Math.ceil(windowMs / 1000)
            });
        }
    });
}

/**
 * 创建API速率限制中间件
 */
function createAPIRateLimitMiddleware() {
    return createRateLimitMiddleware({
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 1000, // API请求限制更宽松
        message: 'API请求过于频繁，请稍后再试'
    });
}

/**
 * 创建登录速率限制中间件
 */
function createLoginRateLimitMiddleware() {
    return createRateLimitMiddleware({
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 5, // 登录尝试次数限制
        message: '登录尝试次数过多，请15分钟后再试',
        skipSuccessfulRequests: true // 成功登录不计入限制
    });
}

/**
 * 创建文件上传速率限制中间件
 */
function createUploadRateLimitMiddleware() {
    return createRateLimitMiddleware({
        windowMs: 60 * 1000, // 1分钟
        max: 10, // 文件上传限制
        message: '文件上传过于频繁，请稍后再试'
    });
}

/**
 * 输入验证中间件
 */
function createInputValidationMiddleware() {
    return (req, res, next) => {
        // 验证和清理输入数据
        if (req.body) {
            req.body = sanitizeObject(req.body);
        }
        
        if (req.query) {
            req.query = sanitizeObject(req.query);
        }
        
        if (req.params) {
            req.params = sanitizeObject(req.params);
        }
        
        next();
    };
}

/**
 * 清理对象中的危险字符
 */
function sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return sanitizeValue(obj);
    }
    
    if (Array.isArray(obj)) {
        return obj.map(item => sanitizeObject(item));
    }
    
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
        const cleanKey = sanitizeValue(key);
        sanitized[cleanKey] = sanitizeObject(value);
    }
    
    return sanitized;
}

/**
 * 清理单个值
 */
function sanitizeValue(value) {
    if (typeof value !== 'string') {
        return value;
    }

    // 如果有validator，使用它；否则使用简单的清理
    if (validator && validator.escape) {
        return validator.escape(value);
    } else {
        // 简单的XSS防护
        return value
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    }
}

/**
 * SQL注入防护中间件
 */
function createSQLInjectionProtectionMiddleware() {
    const suspiciousPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
        /(--|\/\*|\*\/|;|'|"|`)/g,
        /(\bOR\b|\bAND\b).*?[=<>]/gi
    ];
    
    return (req, res, next) => {
        const checkForSQLInjection = (obj, path = '') => {
            if (typeof obj === 'string') {
                for (const pattern of suspiciousPatterns) {
                    if (pattern.test(obj)) {
                        // 只在生产环境记录详细的SQL注入攻击日志
                        if (process.env.NODE_ENV === 'production') {
                            logger.warn('疑似SQL注入攻击', {
                                ip: req.ip,
                                userAgent: req.headers['user-agent'],
                                url: req.originalUrl,
                                method: req.method,
                                suspiciousValue: obj,
                                path
                            });
                        } else {
                            logger.debug('疑似SQL注入攻击 (开发环境)', {
                                url: req.originalUrl,
                                method: req.method,
                                path
                            });
                        }

                        return res.status(400).json({
                            success: false,
                            message: '输入包含非法字符'
                        });
                    }
                }
            } else if (typeof obj === 'object' && obj !== null) {
                for (const [key, value] of Object.entries(obj)) {
                    const result = checkForSQLInjection(value, `${path}.${key}`);
                    if (result) return result;
                }
            }
        };
        
        // 检查请求体、查询参数和路径参数
        const checks = [
            () => checkForSQLInjection(req.body, 'body'),
            () => checkForSQLInjection(req.query, 'query'),
            () => checkForSQLInjection(req.params, 'params')
        ];
        
        for (const check of checks) {
            const result = check();
            if (result) return result;
        }
        
        next();
    };
}

/**
 * 创建Helmet安全头中间件
 */
function createHelmetMiddleware() {
    if (!helmet) {
        // 如果没有安装 helmet，返回一个简单的安全头中间件
        return (req, res, next) => {
            res.setHeader('X-Content-Type-Options', 'nosniff');
            res.setHeader('X-Frame-Options', 'DENY');
            res.setHeader('X-XSS-Protection', '1; mode=block');
            res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
            next();
        };
    }

    return helmet({
        contentSecurityPolicy: process.env.NODE_ENV === 'production' ? {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
                imgSrc: ["'self'", "data:", "blob:"],
                fontSrc: ["'self'"],
                connectSrc: ["'self'"],
                mediaSrc: ["'self'"],
                objectSrc: ["'none'"],
                childSrc: ["'self'"],
                frameSrc: ["'none'"],
                workerSrc: ["'self'"],
                manifestSrc: ["'self'"]
            }
        } : false, // 开发环境禁用CSP
        crossOriginEmbedderPolicy: false, // 避免与某些功能冲突
        crossOriginOpenerPolicy: false, // 开发环境禁用COOP
        crossOriginResourcePolicy: false, // 开发环境禁用CORP
        originAgentCluster: false, // 开发环境禁用Origin-Agent-Cluster
        hsts: process.env.NODE_ENV === 'production' ? {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        } : false // 开发环境禁用HSTS，避免强制HTTPS
    });
}

/**
 * IP地址匹配工具函数
 */
function isIPInRange(ip, range) {
    // 处理localhost特殊情况
    if (range === 'localhost') {
        return ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
    }

    // 处理单个IP
    if (!range.includes('/') && !range.includes('-')) {
        return ip === range;
    }

    // 处理CIDR格式 (如: ***********/24)
    if (range.includes('/')) {
        const [network, prefixLength] = range.split('/');
        const prefix = parseInt(prefixLength);

        // 将IP地址转换为32位整数
        const ipToInt = (ipStr) => {
            return ipStr.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
        };

        const ipInt = ipToInt(ip);
        const networkInt = ipToInt(network);
        const mask = (0xFFFFFFFF << (32 - prefix)) >>> 0;

        return (ipInt & mask) === (networkInt & mask);
    }

    // 处理IP范围格式 (如: ***********-***********00)
    if (range.includes('-')) {
        const [startIP, endIP] = range.split('-');
        const ipToInt = (ipStr) => {
            return ipStr.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
        };

        const ipInt = ipToInt(ip);
        const startInt = ipToInt(startIP.trim());
        const endInt = ipToInt(endIP.trim());

        return ipInt >= startInt && ipInt <= endInt;
    }

    return false;
}

/**
 * 获取客户端真实IP地址
 */
function getClientIP(req) {
    let ip = req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
             req.headers['x-real-ip'] ||
             req.connection.remoteAddress ||
             req.socket.remoteAddress ||
             req.ip ||
             '127.0.0.1';

    // 处理IPv4映射的IPv6地址 (::ffff:*********** -> ***********)
    if (ip.startsWith('::ffff:')) {
        ip = ip.substring(7);
    }

    // 处理IPv6本地地址
    if (ip === '::1') {
        ip = '127.0.0.1';
    }

    return ip;
}

/**
 * IP白名单中间件（增强版）
 */
function createIPWhitelistMiddleware(whitelist = []) {
    return (req, res, next) => {
        if (whitelist.length === 0) {
            return next(); // 如果没有设置白名单，则允许所有IP
        }

        const clientIP = getClientIP(req);

        // 检查IP是否在白名单中
        const isAllowed = whitelist.some(range => isIPInRange(clientIP, range));

        if (!isAllowed) {
            // 只在生产环境记录详细的IP拒绝日志
            if (process.env.NODE_ENV === 'production') {
                logger.warn('IP访问被拒绝', {
                    ip: clientIP,
                    userAgent: req.headers['user-agent'],
                    url: req.originalUrl,
                    method: req.method,
                    whitelist: whitelist
                });
            } else {
                logger.debug('IP访问被拒绝 (开发环境)', {
                    ip: clientIP,
                    url: req.originalUrl,
                    whitelist: whitelist
                });
            }

            return res.status(403).json({
                success: false,
                message: '访问被拒绝：您的IP地址不在允许访问的范围内'
            });
        }

        // 记录允许的访问（仅在开发环境或详细日志模式下）
        if (process.env.NODE_ENV === 'development' || process.env.VERBOSE_LOGS === 'true') {
            logger.debug('IP访问允许', {
                ip: clientIP,
                url: req.originalUrl,
                method: req.method
            });
        }

        next();
    };
}

/**
 * 请求大小限制中间件
 */
function createRequestSizeLimitMiddleware(maxSize = '10mb') {
    return (req, res, next) => {
        const contentLength = req.headers['content-length'];
        
        if (contentLength) {
            const sizeInBytes = parseInt(contentLength);
            const maxSizeInBytes = parseSize(maxSize);
            
            if (sizeInBytes > maxSizeInBytes) {
                // 只在生产环境记录详细的请求大小超限日志
                if (process.env.NODE_ENV === 'production') {
                    logger.warn('请求大小超限', {
                        ip: req.ip,
                        userAgent: req.headers['user-agent'],
                        url: req.originalUrl,
                        method: req.method,
                        contentLength: sizeInBytes,
                        maxSize: maxSizeInBytes
                    });
                } else {
                    logger.debug('请求大小超限 (开发环境)', {
                        url: req.originalUrl,
                        contentLength: sizeInBytes,
                        maxSize: maxSizeInBytes
                    });
                }

                return res.status(413).json({
                    success: false,
                    message: '请求数据过大'
                });
            }
        }
        
        next();
    };
}

/**
 * 解析大小字符串为字节数
 */
function parseSize(size) {
    if (typeof size === 'number') return size;
    
    const units = {
        'b': 1,
        'kb': 1024,
        'mb': 1024 * 1024,
        'gb': 1024 * 1024 * 1024
    };
    
    const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2] || 'b';
    
    return Math.floor(value * units[unit]);
}

/**
 * 安全日志中间件
 */
function createSecurityLogMiddleware() {
    return (req, res, next) => {
        // 记录敏感操作
        const sensitiveEndpoints = [
            '/api/auth/login',
            '/api/auth/logout',
            '/api/users',
            '/api/applications'
        ];
        
        const isSensitive = sensitiveEndpoints.some(endpoint => 
            req.originalUrl.startsWith(endpoint)
        );
        
        if (isSensitive) {
            // 只在生产环境记录敏感操作访问日志
            if (process.env.NODE_ENV === 'production') {
                logger.info('敏感操作访问', {
                    ip: req.ip,
                    userAgent: req.headers['user-agent'],
                    url: req.originalUrl,
                    method: req.method,
                    user: req.user?.id || 'anonymous',
                    timestamp: new Date().toISOString()
                });
            } else {
                // 开发环境只记录debug级别日志
                logger.debug('敏感操作访问 (开发环境)', {
                    url: req.originalUrl,
                    method: req.method,
                    user: req.user?.id || 'anonymous'
                });
            }
        }
        
        next();
    };
}

module.exports = {
    createRateLimitMiddleware,
    createAPIRateLimitMiddleware,
    createLoginRateLimitMiddleware,
    createUploadRateLimitMiddleware,
    createInputValidationMiddleware,
    createSQLInjectionProtectionMiddleware,
    createHelmetMiddleware,
    createIPWhitelistMiddleware,
    createRequestSizeLimitMiddleware,
    createSecurityLogMiddleware
};
