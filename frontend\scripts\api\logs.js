/**
 * 日志管理API模块
 * 封装所有日志相关的API调用
 */

import { API_URL, getAuthHeaders } from './config.js';

/**
 * 获取所有日志文件列表
 * @returns {Promise<Array>} 日志文件列表
 */
export async function getLogFiles() {
    try {
        const response = await axios.get(`${API_URL}/logs/files`, {
            headers: getAuthHeaders()
        });
        return response.data || [];
    } catch (error) {
        console.error('获取日志文件列表失败:', error);
        throw new Error('获取日志文件列表失败: ' + error.message);
    }
}

/**
 * 读取日志文件内容
 * @param {string} fileName - 日志文件名
 * @param {Object} options - 选项参数
 * @param {number} options.limit - 最大行数限制，默认100
 * @param {number} options.skip - 跳过的行数，默认0
 * @returns {Promise<Array>} 日志条目数组
 */
export async function readLogFile(fileName, options = {}) {
    try {
        const { limit = 100, skip = 0 } = options;
        const response = await axios.get(`${API_URL}/logs/files/${encodeURIComponent(fileName)}`, {
            headers: getAuthHeaders(),
            params: { limit, skip }
        });
        return response.data || [];
    } catch (error) {
        console.error('读取日志文件失败:', error);
        throw new Error('读取日志文件失败: ' + error.message);
    }
}

/**
 * 搜索日志文件内容
 * @param {string} fileName - 日志文件名
 * @param {string} searchTerm - 搜索关键词
 * @param {Object} options - 选项参数
 * @param {number} options.limit - 最大行数限制，默认100
 * @returns {Promise<Array>} 匹配的日志条目数组
 */
export async function searchLogFile(fileName, searchTerm, options = {}) {
    try {
        const { limit = 100 } = options;
        const response = await axios.get(`${API_URL}/logs/search/${encodeURIComponent(fileName)}`, {
            headers: getAuthHeaders(),
            params: { term: searchTerm, limit }
        });
        return response.data || [];
    } catch (error) {
        console.error('搜索日志文件失败:', error);
        throw new Error('搜索日志文件失败: ' + error.message);
    }
}

/**
 * 删除日志文件
 * @param {string} fileName - 日志文件名
 * @returns {Promise<boolean>} 是否删除成功
 */
export async function deleteLogFile(fileName) {
    try {
        const response = await axios.delete(`${API_URL}/logs/files/${encodeURIComponent(fileName)}`, {
            headers: getAuthHeaders()
        });
        return response.data.success;
    } catch (error) {
        console.error('删除日志文件失败:', error);
        throw new Error('删除日志文件失败: ' + error.message);
    }
}

/**
 * 清理过期日志文件
 * @param {number} days - 保留天数，默认30天
 * @returns {Promise<Object>} 清理结果 {success, deletedCount, message}
 */
export async function cleanupOldLogs(days = 30) {
    try {
        const response = await axios.post(`${API_URL}/logs/cleanup`,
            { days },
            { headers: getAuthHeaders() }
        );
        return {
            success: response.data.success,
            deletedCount: response.data.deletedCount || 0,
            message: response.data.message || '清理完成'
        };
    } catch (error) {
        console.error('清理过期日志失败:', error);
        throw new Error('清理过期日志失败: ' + error.message);
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间戳
 * @param {string|Date} timestamp - 时间戳
 * @returns {string} 格式化后的时间
 */
export function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 获取日志级别的颜色类名
 * @param {string} level - 日志级别
 * @returns {string} CSS类名
 */
export function getLogLevelColor(level) {
    const colors = {
        error: 'text-red-600 bg-red-50',
        warn: 'text-yellow-600 bg-yellow-50',
        info: 'text-blue-600 bg-blue-50',
        debug: 'text-gray-600 bg-gray-50',
        http: 'text-green-600 bg-green-50'
    };
    return colors[level] || 'text-gray-600 bg-gray-50';
}

/**
 * 高亮搜索关键词
 * @param {string} text - 原始文本
 * @param {string} searchTerm - 搜索关键词
 * @returns {string} 高亮后的HTML
 */
export function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * 日志级别常量
 */
export const LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
    HTTP: 'http'
};

/**
 * 日志级别选项（用于过滤器）
 */
export const LOG_LEVEL_OPTIONS = [
    { value: '', label: '全部级别' },
    { value: LOG_LEVELS.ERROR, label: '错误 (Error)' },
    { value: LOG_LEVELS.WARN, label: '警告 (Warn)' },
    { value: LOG_LEVELS.INFO, label: '信息 (Info)' },
    { value: LOG_LEVELS.DEBUG, label: '调试 (Debug)' },
    { value: LOG_LEVELS.HTTP, label: 'HTTP请求' }
];

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION = {
    limit: 100,
    skip: 0,
    maxLimit: 1000
};
