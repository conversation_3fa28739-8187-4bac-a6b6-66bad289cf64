/**
 * 会话跟踪中间件
 * 记录用户登录、会话活动和性能指标
 */

const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

class SessionTracker {
    constructor() {
        this.db = null;
        this.initDatabase();
    }

    /**
     * 初始化数据库连接
     */
    initDatabase() {
        try {
            const databaseManager = require('../database/database');
            this.db = databaseManager.getConnection();
        } catch (error) {
            logger.error('会话跟踪器数据库初始化失败:', error);
        }
    }

    /**
     * 记录用户登录
     */
    async recordLogin(username, userId, ipAddress, userAgent, success = true, failureReason = null) {
        if (!this.db) return;

        try {
            const sessionId = success ? uuidv4() : null;
            const loginTime = new Date().toISOString();

            // 记录登录日志
            const insertLoginLog = this.db.prepare(`
                INSERT INTO user_login_logs (
                    username, user_id, ip_address, user_agent, 
                    login_time, success, failure_reason, session_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `);

            insertLoginLog.run(
                username, userId, ipAddress, userAgent,
                loginTime, success ? 1 : 0, failureReason, sessionId
            );

            // 如果登录成功，创建会话记录
            if (success && userId) {
                const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24小时后过期

                const insertSession = this.db.prepare(`
                    INSERT OR REPLACE INTO user_sessions (
                        id, user_id, username, ip_address, user_agent,
                        login_time, last_activity, expires_at, active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                `);

                insertSession.run(
                    sessionId, userId, username, ipAddress, userAgent,
                    loginTime, loginTime, expiresAt
                );

                return sessionId;
            }
        } catch (error) {
            logger.error('记录用户登录失败:', error);
        }

        return null;
    }

    /**
     * 更新会话活动
     */
    async updateSessionActivity(sessionId, userId) {
        if (!this.db || !sessionId) return;

        try {
            const lastActivity = new Date().toISOString();
            const updateSession = this.db.prepare(`
                UPDATE user_sessions 
                SET last_activity = ?, updated_at = ?
                WHERE id = ? AND user_id = ? AND active = 1
            `);

            updateSession.run(lastActivity, lastActivity, sessionId, userId);
        } catch (error) {
            logger.error('更新会话活动失败:', error);
        }
    }

    /**
     * 记录用户登出
     */
    async recordLogout(sessionId, userId) {
        if (!this.db || !sessionId) return;

        try {
            const logoutTime = new Date().toISOString();

            // 更新登录日志的登出时间
            const updateLoginLog = this.db.prepare(`
                UPDATE user_login_logs 
                SET logout_time = ?
                WHERE session_id = ? AND user_id = ?
            `);

            updateLoginLog.run(logoutTime, sessionId, userId);

            // 标记会话为非活跃
            const updateSession = this.db.prepare(`
                UPDATE user_sessions 
                SET active = 0, updated_at = ?
                WHERE id = ? AND user_id = ?
            `);

            updateSession.run(logoutTime, sessionId, userId);
        } catch (error) {
            logger.error('记录用户登出失败:', error);
        }
    }

    /**
     * 记录API性能
     */
    async recordPerformance(endpoint, method, responseTime, statusCode, userId = null, ipAddress = null, errorMessage = null) {
        if (!this.db) return;

        try {
            const insertPerformance = this.db.prepare(`
                INSERT INTO performance_logs (
                    endpoint, method, response_time, status_code,
                    user_id, ip_address, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            insertPerformance.run(
                endpoint, method, responseTime, statusCode,
                userId, ipAddress, errorMessage
            );
        } catch (error) {
            logger.error('记录API性能失败:', error);
        }
    }

    /**
     * 记录系统指标
     */
    async recordSystemMetric(metricType, metricValue, metricUnit = null, additionalData = {}) {
        if (!this.db) return;

        try {
            const insertMetric = this.db.prepare(`
                INSERT INTO system_metrics (
                    metric_type, metric_value, metric_unit, additional_data
                ) VALUES (?, ?, ?, ?)
            `);

            insertMetric.run(
                metricType, metricValue, metricUnit, JSON.stringify(additionalData)
            );
        } catch (error) {
            logger.error('记录系统指标失败:', error);
        }
    }

    /**
     * 清理过期会话
     */
    async cleanupExpiredSessions() {
        if (!this.db) return;

        try {
            const now = new Date().toISOString();
            
            // 标记过期会话为非活跃
            const updateExpiredSessions = this.db.prepare(`
                UPDATE user_sessions 
                SET active = 0, updated_at = ?
                WHERE expires_at < ? AND active = 1
            `);

            const result = updateExpiredSessions.run(now, now);
            
            if (result.changes > 0) {
                logger.info(`清理了 ${result.changes} 个过期会话`);
            }
        } catch (error) {
            logger.error('清理过期会话失败:', error);
        }
    }

    /**
     * 清理旧的日志记录
     */
    async cleanupOldLogs(daysToKeep = 30) {
        if (!this.db) return;

        try {
            const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();

            // 清理旧的登录日志
            const deleteOldLoginLogs = this.db.prepare(`
                DELETE FROM user_login_logs 
                WHERE login_time < ?
            `);

            const loginLogsResult = deleteOldLoginLogs.run(cutoffDate);

            // 清理旧的性能日志
            const deleteOldPerformanceLogs = this.db.prepare(`
                DELETE FROM performance_logs 
                WHERE created_at < ?
            `);

            const performanceLogsResult = deleteOldPerformanceLogs.run(cutoffDate);

            // 清理旧的系统指标
            const deleteOldMetrics = this.db.prepare(`
                DELETE FROM system_metrics 
                WHERE recorded_at < ?
            `);

            const metricsResult = deleteOldMetrics.run(cutoffDate);

            logger.info(`清理了 ${loginLogsResult.changes} 条登录日志, ${performanceLogsResult.changes} 条性能日志, ${metricsResult.changes} 条系统指标`);
        } catch (error) {
            logger.error('清理旧日志失败:', error);
        }
    }

    /**
     * 性能监控中间件
     */
    performanceMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            const originalSend = res.send;

            res.send = function(data) {
                const responseTime = Date.now() - startTime;
                const endpoint = req.route ? req.route.path : req.path;
                const method = req.method;
                const statusCode = res.statusCode;
                const userId = req.user ? req.user.id : null;
                const ipAddress = req.ip || req.connection.remoteAddress;
                const errorMessage = statusCode >= 400 ? data : null;

                // 异步记录性能数据
                setImmediate(() => {
                    sessionTracker.recordPerformance(
                        endpoint, method, responseTime, statusCode,
                        userId, ipAddress, errorMessage
                    );
                });

                originalSend.call(this, data);
            };

            next();
        };
    }

    /**
     * 会话活动更新中间件
     */
    sessionActivityMiddleware() {
        return (req, res, next) => {
            if (req.user && req.sessionId) {
                // 异步更新会话活动
                setImmediate(() => {
                    this.updateSessionActivity(req.sessionId, req.user.id);
                });
            }
            next();
        };
    }
}

// 创建全局实例
const sessionTracker = new SessionTracker();

// 定期清理任务
setInterval(() => {
    sessionTracker.cleanupExpiredSessions();
}, 60 * 60 * 1000); // 每小时清理一次过期会话

setInterval(() => {
    sessionTracker.cleanupOldLogs(30);
}, 24 * 60 * 60 * 1000); // 每天清理一次旧日志

module.exports = sessionTracker;
