/**
 * 产品数据访问层
 * 处理产品相关的数据库操作
 */

const databaseManager = require('./database');
const { ProductModel, ProductionProcessModel } = require('../models/productModel');
const logger = require('../utils/logger');

class ProductRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        // 只在详细日志模式下显示初始化信息
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('产品数据访问层初始化完成');
        }
    }

    /**
     * 创建产品
     * @param {ProductModel} product 产品模型
     * @returns {Promise<ProductModel>} 创建的产品
     */
    async create(product) {
        try {
            const dbData = product.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO products (
                    id, code, name, category, specifications, unit, standard_time, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.code, dbData.name, dbData.category,
                dbData.specifications, dbData.unit, dbData.standard_time,
                dbData.created_at, dbData.updated_at
            );

            logger.info('产品创建成功', { productId: product.id });
            return product;
        } catch (error) {
            logger.error('产品创建失败', { error: error.message, productId: product.id });
            throw error;
        }
    }

    /**
     * 根据ID获取产品
     * @param {string} id 产品ID
     * @returns {Promise<ProductModel|null>} 产品模型
     */
    async findById(id) {
        try {
            const stmt = this.db.prepare('SELECT * FROM products WHERE id = ?');
            const row = stmt.get(id);
            
            return row ? ProductModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据ID获取产品失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 根据产品编码获取产品
     * @param {string} code 产品编码
     * @returns {Promise<ProductModel|null>} 产品模型
     */
    async findByCode(code) {
        try {
            const stmt = this.db.prepare('SELECT * FROM products WHERE code = ?');
            const row = stmt.get(code);
            
            return row ? ProductModel.fromDatabase(row) : null;
        } catch (error) {
            logger.error('根据编码获取产品失败', { error: error.message, code });
            throw error;
        }
    }

    /**
     * 获取产品列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 产品列表和总数
     */
    async findAll(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                category,
                keyword
            } = options;

            let whereClause = '';
            const params = [];

            // 构建查询条件
            const conditions = [];

            if (category) {
                conditions.push('category = ?');
                params.push(category);
            }

            if (keyword) {
                conditions.push('(name LIKE ? OR code LIKE ?)');
                params.push(`%${keyword}%`, `%${keyword}%`);
            }

            if (conditions.length > 0) {
                whereClause = 'WHERE ' + conditions.join(' AND ');
            }

            // 获取总数
            const countStmt = this.db.prepare(`SELECT COUNT(*) as total FROM products ${whereClause}`);
            const { total } = countStmt.get(...params);

            // 获取分页数据
            const offset = (page - 1) * limit;
            const dataStmt = this.db.prepare(`
                SELECT * FROM products ${whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `);

            const rows = dataStmt.all(...params, limit, offset);
            const products = rows.map(row => ProductModel.fromDatabase(row));

            return {
                products,
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            logger.error('获取产品列表失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 更新产品
     * @param {string} id 产品ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<ProductModel|null>} 更新后的产品
     */
    async update(id, updateData) {
        try {
            const existingProduct = await this.findById(id);
            if (!existingProduct) {
                return null;
            }

            // 合并更新数据
            const updatedProduct = new ProductModel({
                ...existingProduct,
                ...updateData,
                id: id, // 确保ID不被覆盖
                updatedAt: new Date().toISOString()
            });

            const dbData = updatedProduct.toDatabase();

            const stmt = this.db.prepare(`
                UPDATE products SET
                    code = ?, name = ?, category = ?, specifications = ?,
                    unit = ?, standard_time = ?, updated_at = ?
                WHERE id = ?
            `);

            stmt.run(
                dbData.code, dbData.name, dbData.category, dbData.specifications,
                dbData.unit, dbData.standard_time, dbData.updated_at, id
            );

            logger.info('产品更新成功', { productId: id });
            return updatedProduct;
        } catch (error) {
            logger.error('产品更新失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 删除产品
     * @param {string} id 产品ID
     * @returns {Promise<boolean>} 删除结果
     */
    async delete(id) {
        try {
            const stmt = this.db.prepare('DELETE FROM products WHERE id = ?');
            const result = stmt.run(id);
            const success = result.changes > 0;

            if (success) {
                logger.info('产品删除成功', { productId: id });
            }

            return success;
        } catch (error) {
            logger.error('产品删除失败', { error: error.message, productId: id });
            throw error;
        }
    }

    /**
     * 获取产品的生产工艺流程
     * @param {string} productId 产品ID
     * @returns {Promise<Array>} 工艺流程列表
     */
    async getProductionProcesses(productId) {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM production_processes 
                WHERE product_id = ? 
                ORDER BY sequence_order ASC
            `);
            const rows = stmt.all(productId);
            
            return rows.map(row => ProductionProcessModel.fromDatabase(row));
        } catch (error) {
            logger.error('获取产品工艺流程失败', { error: error.message, productId });
            throw error;
        }
    }

    /**
     * 创建生产工艺流程
     * @param {ProductionProcessModel} process 工艺流程模型
     * @returns {Promise<ProductionProcessModel>} 创建的工艺流程
     */
    async createProcess(process) {
        try {
            const dbData = process.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO production_processes (
                    id, product_id, process_name, sequence_order, standard_time,
                    required_equipment_type, skill_requirements, setup_time, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.product_id, dbData.process_name, dbData.sequence_order,
                dbData.standard_time, dbData.required_equipment_type, dbData.skill_requirements,
                dbData.setup_time, dbData.created_at
            );

            logger.info('工艺流程创建成功', { processId: process.id });
            return process;
        } catch (error) {
            logger.error('工艺流程创建失败', { error: error.message, processId: process.id });
            throw error;
        }
    }

    /**
     * 删除产品的所有工艺流程
     * @param {string} productId 产品ID
     * @returns {Promise<boolean>} 删除结果
     */
    async deleteProcessesByProductId(productId) {
        try {
            const stmt = this.db.prepare('DELETE FROM production_processes WHERE product_id = ?');
            const result = stmt.run(productId);

            logger.info('产品工艺流程删除成功', { productId, deletedCount: result.changes });
            return true;
        } catch (error) {
            logger.error('删除产品工艺流程失败', { error: error.message, productId });
            throw error;
        }
    }
}

module.exports = ProductRepository;
