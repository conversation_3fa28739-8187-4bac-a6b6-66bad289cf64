/**
 * 成品管理控制器
 * 处理成品相关的HTTP请求
 */

const ProductsService = require('../services/productsService');
const logger = require('../../../utils/logger');

class ProductsController {
    /**
     * 获取成品列表
     */
    async getProducts(req, res) {
        try {
            const filters = {
                product_type: req.query.product_type,
                status: req.query.status,
                search: req.query.search,
                limit: req.query.limit
            };

            const products = await ProductsService.getProducts(filters);
            
            res.json({
                success: true,
                data: products,
                message: '获取成品列表成功'
            });
        } catch (error) {
            logger.error('获取成品列表失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取成品列表失败'
            });
        }
    }

    /**
     * 根据ID获取成品详情
     */
    async getProductById(req, res) {
        try {
            const { id } = req.params;
            const product = await ProductsService.getProductById(id);
            
            res.json({
                success: true,
                data: product,
                message: '获取成品详情成功'
            });
        } catch (error) {
            logger.error('获取成品详情失败:', error);
            res.status(error.message === '成品不存在' ? 404 : 500).json({
                success: false,
                message: error.message || '获取成品详情失败'
            });
        }
    }

    /**
     * 创建成品
     */
    async createProduct(req, res) {
        try {
            const productData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['product_code', 'product_name', 'product_type', 'unit'];
            for (const field of requiredFields) {
                if (!productData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            const result = await ProductsService.createProduct(productData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '成品创建成功'
            });
        } catch (error) {
            logger.error('创建成品失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '创建成品失败'
            });
        }
    }

    /**
     * 更新成品
     */
    async updateProduct(req, res) {
        try {
            const { id } = req.params;
            const productData = req.body;
            const operatorId = req.user.id;

            const result = await ProductsService.updateProduct(id, productData, operatorId);
            
            res.json({
                success: true,
                data: result,
                message: '成品更新成功'
            });
        } catch (error) {
            logger.error('更新成品失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 400).json({
                success: false,
                message: error.message || '更新成品失败'
            });
        }
    }

    /**
     * 删除成品
     */
    async deleteProduct(req, res) {
        try {
            const { id } = req.params;
            const operatorId = req.user.id;

            const result = await ProductsService.deleteProduct(id, operatorId);
            
            res.json({
                success: true,
                data: result,
                message: '成品删除成功'
            });
        } catch (error) {
            logger.error('删除成品失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 400).json({
                success: false,
                message: error.message || '删除成品失败'
            });
        }
    }

    /**
     * 成品入库
     */
    async productInbound(req, res) {
        try {
            const inboundData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['product_id', 'quantity'];
            for (const field of requiredFields) {
                if (!inboundData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (inboundData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '入库数量必须大于0'
                });
            }

            const result = await ProductsService.productInbound(inboundData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '成品入库成功'
            });
        } catch (error) {
            logger.error('成品入库失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '成品入库失败'
            });
        }
    }

    /**
     * 成品出库
     */
    async productOutbound(req, res) {
        try {
            const outboundData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['product_id', 'quantity'];
            for (const field of requiredFields) {
                if (!outboundData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (outboundData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '出库数量必须大于0'
                });
            }

            const result = await ProductsService.productOutbound(outboundData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '成品出库成功'
            });
        } catch (error) {
            logger.error('成品出库失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '成品出库失败'
            });
        }
    }

    /**
     * 成品返仓
     */
    async productReturn(req, res) {
        try {
            const returnData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['product_id', 'quantity'];
            for (const field of requiredFields) {
                if (!returnData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (returnData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '返仓数量必须大于0'
                });
            }

            const result = await ProductsService.productReturn(returnData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '成品返仓成功'
            });
        } catch (error) {
            logger.error('成品返仓失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '成品返仓失败'
            });
        }
    }

    /**
     * 获取成品库存报告
     */
    async getProductsInventory(req, res) {
        try {
            const filters = {
                stock_status: req.query.stock_status
            };

            const inventory = await ProductsService.getProductsInventory(filters);
            
            res.json({
                success: true,
                data: inventory,
                message: '获取成品库存报告成功'
            });
        } catch (error) {
            logger.error('获取成品库存报告失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取成品库存报告失败'
            });
        }
    }
}

module.exports = new ProductsController();
