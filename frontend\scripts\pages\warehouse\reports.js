/**
 * 仓库管理 - 报表追溯页面
 * 提供报表生成、追溯查询和数据分析功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import { 
    getMaterials, getProducts, getTransactions, getReports,
    validateQRCode, formatDateTime, getOperationTypeText
} from '../../api/warehouse.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    requiredPermissions: ['warehouse_view'], // 需要仓库查看权限
    components: {
        Sidebar
    },
    setup() {
        const { ref, reactive, onMounted, computed } = Vue;

        // 权限检查
        const hasPermission = (permission) => {
            const user = window.currentUser;
            return user && user.permissions && user.permissions.includes(permission);
        };

        // 权限计算属性
        const canManage = computed(() => hasPermission('warehouse_manage'));

        // 页面状态
        const activeTab = ref('reports'); // 'reports' | 'traceability' | 'analytics'
        const isGeneratingReport = ref(false);
        const isTracing = ref(false);

        // 报表生成状态
        const selectedReportType = ref(''); // 'inventory' | 'transactions' | 'summary'
        const reportConfig = reactive({
            name: '',
            startDate: '',
            endDate: '',
            itemType: '',
            operationType: '',
            format: 'excel',
            // 库存报表选项
            includeNormal: true,
            includeLow: true,
            includeCritical: true,
            includeOut: true,
            // 汇总报表选项
            includeDaily: true,
            includeWeekly: false,
            includeMonthly: false
        });
        const reportPreview = ref(null);

        // 追溯查询状态
        const traceMethod = ref(''); // 'qrcode' | 'manual'
        const traceQRCode = ref('');
        const manualTrace = reactive({
            type: '',
            method: 'code',
            query: ''
        });
        const traceResults = ref(null);

        // 页面初始化
        onMounted(() => {
            // 设置默认日期范围（最近30天）
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            reportConfig.endDate = today.toISOString().split('T')[0];
            reportConfig.startDate = thirtyDaysAgo.toISOString().split('T')[0];
        });

        // 报表生成相关方法
        function selectReportType(type) {
            selectedReportType.value = type;
            reportConfig.name = getDefaultReportName();
            reportPreview.value = null;
        }

        function getDefaultReportName() {
            const typeNames = {
                'inventory': '库存报表',
                'transactions': '事务报表',
                'summary': '汇总报表'
            };
            const today = new Date().toISOString().split('T')[0];
            return `${typeNames[selectedReportType.value] || '报表'}_${today}`;
        }

        // 预览报表
        async function previewReport() {
            if (!selectedReportType.value) {
                alert('请选择报表类型');
                return;
            }

            try {
                isGeneratingReport.value = true;
                
                const params = {
                    type: selectedReportType.value,
                    startDate: reportConfig.startDate,
                    endDate: reportConfig.endDate,
                    itemType: reportConfig.itemType,
                    operationType: reportConfig.operationType,
                    preview: true,
                    limit: 10
                };

                // 添加特定参数
                if (selectedReportType.value === 'inventory') {
                    params.includeNormal = reportConfig.includeNormal;
                    params.includeLow = reportConfig.includeLow;
                    params.includeCritical = reportConfig.includeCritical;
                    params.includeOut = reportConfig.includeOut;
                } else if (selectedReportType.value === 'summary') {
                    params.includeDaily = reportConfig.includeDaily;
                    params.includeWeekly = reportConfig.includeWeekly;
                    params.includeMonthly = reportConfig.includeMonthly;
                }

                const response = await getReports(params);
                
                if (response.success) {
                    reportPreview.value = response.data;
                } else {
                    alert('预览失败: ' + response.message);
                }
            } catch (error) {
                console.error('预览失败:', error);
                alert('预览失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isGeneratingReport.value = false;
            }
        }

        // 生成报表
        async function generateReport() {
            if (!selectedReportType.value) {
                alert('请选择报表类型');
                return;
            }

            try {
                isGeneratingReport.value = true;
                
                const params = {
                    type: selectedReportType.value,
                    name: reportConfig.name || getDefaultReportName(),
                    startDate: reportConfig.startDate,
                    endDate: reportConfig.endDate,
                    itemType: reportConfig.itemType,
                    operationType: reportConfig.operationType,
                    format: reportConfig.format
                };

                // 添加特定参数
                if (selectedReportType.value === 'inventory') {
                    params.includeNormal = reportConfig.includeNormal;
                    params.includeLow = reportConfig.includeLow;
                    params.includeCritical = reportConfig.includeCritical;
                    params.includeOut = reportConfig.includeOut;
                } else if (selectedReportType.value === 'summary') {
                    params.includeDaily = reportConfig.includeDaily;
                    params.includeWeekly = reportConfig.includeWeekly;
                    params.includeMonthly = reportConfig.includeMonthly;
                }

                const response = await getReports(params);
                
                if (response.success) {
                    // 下载报表文件
                    const blob = new Blob([response.data], { 
                        type: getContentType(reportConfig.format) 
                    });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `${params.name}.${getFileExtension(reportConfig.format)}`;
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    
                    alert('报表生成成功！');
                } else {
                    alert('报表生成失败: ' + response.message);
                }
            } catch (error) {
                console.error('报表生成失败:', error);
                alert('报表生成失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isGeneratingReport.value = false;
            }
        }

        // 追溯查询相关方法
        function selectTraceMethod(method) {
            traceMethod.value = method;
            traceResults.value = null;
            traceQRCode.value = '';
            Object.assign(manualTrace, {
                type: '',
                method: 'code',
                query: ''
            });
        }

        // 二维码追溯
        async function traceByQRCode() {
            if (!traceQRCode.value.trim()) {
                alert('请输入二维码');
                return;
            }

            try {
                isTracing.value = true;
                traceResults.value = [];
                
                // 首先验证二维码
                const qrResponse = await validateQRCode(traceQRCode.value.trim());
                
                if (!qrResponse.success) {
                    alert('二维码验证失败: ' + qrResponse.message);
                    return;
                }

                const qrInfo = qrResponse.data;
                
                // 根据二维码信息查询追溯记录
                const params = {
                    item_type: qrInfo.item_type,
                    item_id: qrInfo.item_id,
                    sort: 'created_at',
                    order: 'desc',
                    limit: 100
                };

                const response = await getTransactions(params);
                
                if (response.success) {
                    traceResults.value = response.data || [];
                    if (traceResults.value.length === 0) {
                        alert('未找到该二维码的追溯记录');
                    }
                } else {
                    alert('追溯查询失败: ' + response.message);
                }
            } catch (error) {
                console.error('追溯查询失败:', error);
                alert('追溯查询失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isTracing.value = false;
            }
        }

        // 手动追溯
        async function traceManually() {
            if (!manualTrace.query.trim()) {
                alert('请输入查询内容');
                return;
            }

            try {
                isTracing.value = true;
                traceResults.value = [];
                
                const params = {
                    sort: 'created_at',
                    order: 'desc',
                    limit: 100
                };

                // 根据查询方式设置参数
                if (manualTrace.method === 'code') {
                    params.item_code = manualTrace.query.trim();
                } else if (manualTrace.method === 'name') {
                    params.item_name = manualTrace.query.trim();
                } else if (manualTrace.method === 'transaction') {
                    params.transaction_id = manualTrace.query.trim();
                }

                // 如果指定了类型，添加类型筛选
                if (manualTrace.type) {
                    params.item_type = manualTrace.type;
                }

                const response = await getTransactions(params);
                
                if (response.success) {
                    traceResults.value = response.data || [];
                    if (traceResults.value.length === 0) {
                        alert('未找到符合条件的追溯记录');
                    }
                } else {
                    alert('追溯查询失败: ' + response.message);
                }
            } catch (error) {
                console.error('追溯查询失败:', error);
                alert('追溯查询失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isTracing.value = false;
            }
        }

        // 辅助方法
        function getTracePlaceholder() {
            const placeholders = {
                'code': '请输入编号',
                'name': '请输入名称',
                'transaction': '请输入事务ID'
            };
            return placeholders[manualTrace.method] || '请输入查询内容';
        }

        function getContentType(format) {
            const types = {
                'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'csv': 'text/csv;charset=utf-8;',
                'pdf': 'application/pdf'
            };
            return types[format] || 'application/octet-stream';
        }

        function getFileExtension(format) {
            const extensions = {
                'excel': 'xlsx',
                'csv': 'csv',
                'pdf': 'pdf'
            };
            return extensions[format] || 'txt';
        }

        return {
            // 状态
            activeTab,
            isGeneratingReport,
            isTracing,
            // 报表生成
            selectedReportType,
            reportConfig,
            reportPreview,
            // 追溯查询
            traceMethod,
            traceQRCode,
            manualTrace,
            traceResults,
            // 方法
            selectReportType,
            getDefaultReportName,
            previewReport,
            generateReport,
            selectTraceMethod,
            traceByQRCode,
            traceManually,
            getTracePlaceholder,
            formatDateTime,
            getOperationTypeText,
            // 权限检查
            hasPermission,
            canManage
        };
    },
    onUserLoaded: async (user) => {
        console.log('报表追溯页面加载完成，当前用户:', user.username);
        // 设置全局用户信息，供权限检查使用
        window.currentUser = user;
    }
}).mount('#app');
