/**
 * 创建用户跟踪相关表
 * 包括登录日志表、用户会话表和性能监控表
 */

const logger = require('../../utils/logger');

class UserTrackingTablesMigration {
    constructor(database) {
        this.db = database;
    }

    /**
     * 执行迁移
     */
    async migrate() {
        try {
            // 创建用户登录日志表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS user_login_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL,
                    user_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    login_time TEXT NOT NULL,
                    logout_time TEXT,
                    success INTEGER NOT NULL DEFAULT 1,
                    failure_reason TEXT,
                    session_id TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // 创建用户会话表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    username TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    login_time TEXT NOT NULL,
                    last_activity TEXT NOT NULL,
                    expires_at TEXT NOT NULL,
                    active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // 创建性能监控表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS performance_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    endpoint TEXT NOT NULL,
                    method TEXT NOT NULL,
                    response_time INTEGER NOT NULL,
                    status_code INTEGER NOT NULL,
                    user_id TEXT,
                    ip_address TEXT,
                    error_message TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `);

            // 创建系统监控表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_type TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_unit TEXT,
                    additional_data TEXT DEFAULT '{}',
                    recorded_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 创建相关索引
            this.createIndexes();

            logger.info('用户跟踪表创建成功');
            return { success: true, message: '用户跟踪表创建成功' };
        } catch (error) {
            logger.error('创建用户跟踪表失败:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * 创建索引
     */
    createIndexes() {
        const indexes = [
            // 登录日志索引
            'CREATE INDEX IF NOT EXISTS idx_user_login_logs_username ON user_login_logs (username)',
            'CREATE INDEX IF NOT EXISTS idx_user_login_logs_user_id ON user_login_logs (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_login_logs_login_time ON user_login_logs (login_time)',
            'CREATE INDEX IF NOT EXISTS idx_user_login_logs_success ON user_login_logs (success)',
            'CREATE INDEX IF NOT EXISTS idx_user_login_logs_session_id ON user_login_logs (session_id)',
            
            // 用户会话索引
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_username ON user_sessions (username)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions (last_activity)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions (active)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions (expires_at)',
            
            // 性能监控索引
            'CREATE INDEX IF NOT EXISTS idx_performance_logs_endpoint ON performance_logs (endpoint)',
            'CREATE INDEX IF NOT EXISTS idx_performance_logs_method ON performance_logs (method)',
            'CREATE INDEX IF NOT EXISTS idx_performance_logs_created_at ON performance_logs (created_at)',
            'CREATE INDEX IF NOT EXISTS idx_performance_logs_response_time ON performance_logs (response_time)',
            'CREATE INDEX IF NOT EXISTS idx_performance_logs_status_code ON performance_logs (status_code)',
            
            // 系统监控索引
            'CREATE INDEX IF NOT EXISTS idx_system_metrics_metric_type ON system_metrics (metric_type)',
            'CREATE INDEX IF NOT EXISTS idx_system_metrics_recorded_at ON system_metrics (recorded_at)'
        ];

        indexes.forEach(indexSql => {
            try {
                this.db.exec(indexSql);
            } catch (error) {
                logger.warn(`创建索引失败: ${error.message}`);
            }
        });
    }

    /**
     * 回滚迁移
     */
    async rollback() {
        try {
            this.db.exec('DROP TABLE IF EXISTS user_login_logs');
            this.db.exec('DROP TABLE IF EXISTS user_sessions');
            this.db.exec('DROP TABLE IF EXISTS performance_logs');
            this.db.exec('DROP TABLE IF EXISTS system_metrics');
            
            logger.info('用户跟踪表删除成功');
            return { success: true, message: '用户跟踪表删除成功' };
        } catch (error) {
            logger.error('删除用户跟踪表失败:', error);
            return { success: false, message: error.message };
        }
    }
}

module.exports = UserTrackingTablesMigration;
