/**
 * 仓库管理模块入口
 * 统一导出所有仓库管理相关功能
 */

const express = require('express');
const router = express.Router();

// 导入控制器
const materialsController = require('./controllers/materialsController');
const productsController = require('./controllers/productsController');
const transactionsController = require('./controllers/transactionsController');
const qrcodesController = require('./controllers/qrcodesController');
const reportsController = require('./controllers/reportsController');

// 导入中间件
const { authenticateJWT, checkPermission } = require('../../middlewares/auth');

// 物料管理路由
router.get('/materials', authenticateJWT, checkPermission('warehouse_view'), materialsController.getMaterials);
router.post('/materials', authenticateJWT, checkPermission('warehouse_manage'), materialsController.createMaterial);
router.put('/materials/:id', authenticateJWT, checkPermission('warehouse_manage'), materialsController.updateMaterial);
router.delete('/materials/:id', authenticateJWT, checkPermission('warehouse_manage'), materialsController.deleteMaterial);
router.get('/materials/:id', authenticateJWT, checkPermission('warehouse_view'), materialsController.getMaterialById);

// 物料入库/出库/退料路由
router.post('/materials/inbound', authenticateJWT, checkPermission('warehouse_inbound'), materialsController.materialInbound);
router.post('/materials/outbound', authenticateJWT, checkPermission('warehouse_outbound'), materialsController.materialOutbound);
router.post('/materials/return', authenticateJWT, checkPermission('warehouse_return'), materialsController.materialReturn);

// 成品管理路由
router.get('/products', authenticateJWT, checkPermission('warehouse_view'), productsController.getProducts);
router.post('/products', authenticateJWT, checkPermission('warehouse_manage'), productsController.createProduct);
router.put('/products/:id', authenticateJWT, checkPermission('warehouse_manage'), productsController.updateProduct);
router.delete('/products/:id', authenticateJWT, checkPermission('warehouse_manage'), productsController.deleteProduct);
router.get('/products/:id', authenticateJWT, checkPermission('warehouse_view'), productsController.getProductById);

// 成品入库/出库/返仓路由
router.post('/products/inbound', authenticateJWT, checkPermission('warehouse_inbound'), productsController.productInbound);
router.post('/products/outbound', authenticateJWT, checkPermission('warehouse_outbound'), productsController.productOutbound);
router.post('/products/return', authenticateJWT, checkPermission('warehouse_return'), productsController.productReturn);

// 库存事务路由
router.get('/transactions', authenticateJWT, checkPermission('warehouse_view'), transactionsController.getTransactions);
router.get('/transactions/:id', authenticateJWT, checkPermission('warehouse_view'), transactionsController.getTransactionById);
router.get('/transactions/item/:itemType/:itemId', authenticateJWT, checkPermission('warehouse_view'), transactionsController.getTransactionsByItem);

// 二维码管理路由
router.post('/qrcodes/generate', authenticateJWT, checkPermission('warehouse_manage'), qrcodesController.generateQRCode);
router.post('/qrcodes/validate', authenticateJWT, checkPermission('warehouse_view'), qrcodesController.validateQRCode);
router.get('/qrcodes/:qrcode', authenticateJWT, checkPermission('warehouse_view'), qrcodesController.getQRCodeInfo);
router.put('/qrcodes/:id/use', authenticateJWT, checkPermission('warehouse_outbound'), qrcodesController.useQRCode);

// 库存查询路由
router.get('/inventory/materials', authenticateJWT, checkPermission('warehouse_view'), materialsController.getMaterialsInventory);
router.get('/inventory/products', authenticateJWT, checkPermission('warehouse_view'), productsController.getProductsInventory);
router.get('/inventory/alerts', authenticateJWT, checkPermission('warehouse_view'), reportsController.getInventoryAlerts);

// 报表路由
router.get('/reports/inventory', authenticateJWT, checkPermission('warehouse_view'), reportsController.getInventoryReport);
router.get('/reports/transactions', authenticateJWT, checkPermission('warehouse_view'), reportsController.getTransactionReport);
router.get('/reports/traceability/:qrcode', authenticateJWT, checkPermission('warehouse_view'), reportsController.getTraceabilityReport);

module.exports = router;
