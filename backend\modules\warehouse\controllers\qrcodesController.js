/**
 * 二维码管理控制器
 * 处理二维码相关的HTTP请求
 */

const QRCodesService = require('../services/qrcodesService');
const logger = require('../../../utils/logger');

class QRCodesController {
    /**
     * 生成二维码
     */
    async generateQRCode(req, res) {
        try {
            const qrcodeData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['item_type', 'item_id'];
            for (const field of requiredFields) {
                if (!qrcodeData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证item_type
            if (!['material', 'product'].includes(qrcodeData.item_type)) {
                return res.status(400).json({
                    success: false,
                    message: 'item_type必须是material或product'
                });
            }

            const result = await QRCodesService.generateQRCode(qrcodeData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '二维码生成成功'
            });
        } catch (error) {
            logger.error('生成二维码失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '生成二维码失败'
            });
        }
    }

    /**
     * 验证二维码
     */
    async validateQRCode(req, res) {
        try {
            const { qrcode, expected_item_type, expected_item_id } = req.body;

            if (!qrcode) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必填字段: qrcode'
                });
            }

            const result = await QRCodesService.validateQRCode(qrcode, expected_item_type, expected_item_id);
            
            res.json({
                success: true,
                data: result,
                message: '二维码验证成功'
            });
        } catch (error) {
            logger.error('验证二维码失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '验证二维码失败'
            });
        }
    }

    /**
     * 获取二维码信息
     */
    async getQRCodeInfo(req, res) {
        try {
            const { qrcode } = req.params;
            const result = await QRCodesService.getQRCodeInfo(qrcode);
            
            res.json({
                success: true,
                data: result,
                message: '获取二维码信息成功'
            });
        } catch (error) {
            logger.error('获取二维码信息失败:', error);
            res.status(error.message === '二维码不存在' ? 404 : 500).json({
                success: false,
                message: error.message || '获取二维码信息失败'
            });
        }
    }

    /**
     * 使用二维码
     */
    async useQRCode(req, res) {
        try {
            const { id } = req.params;
            const { notes } = req.body;
            const operatorId = req.user.id;

            const result = await QRCodesService.useQRCode(id, operatorId, notes);
            
            res.json({
                success: true,
                data: result,
                message: '二维码使用成功'
            });
        } catch (error) {
            logger.error('使用二维码失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 400).json({
                success: false,
                message: error.message || '使用二维码失败'
            });
        }
    }

    /**
     * 批量生成二维码
     */
    async batchGenerateQRCodes(req, res) {
        try {
            const batchData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['item_type', 'item_id', 'quantity'];
            for (const field of requiredFields) {
                if (!batchData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证item_type
            if (!['material', 'product'].includes(batchData.item_type)) {
                return res.status(400).json({
                    success: false,
                    message: 'item_type必须是material或product'
                });
            }

            // 验证数量
            if (batchData.quantity <= 0 || batchData.quantity > 1000) {
                return res.status(400).json({
                    success: false,
                    message: '批量生成数量必须在1-1000之间'
                });
            }

            const result = await QRCodesService.batchGenerateQRCodes(batchData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: `批量生成${result.length}个二维码成功`
            });
        } catch (error) {
            logger.error('批量生成二维码失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '批量生成二维码失败'
            });
        }
    }

    /**
     * 获取二维码列表
     */
    async getQRCodes(req, res) {
        try {
            const filters = {
                item_type: req.query.item_type,
                item_id: req.query.item_id,
                status: req.query.status,
                batch_number: req.query.batch_number,
                limit: req.query.limit
            };

            const qrcodes = await QRCodesService.getQRCodes(filters);
            
            res.json({
                success: true,
                data: qrcodes,
                message: '获取二维码列表成功'
            });
        } catch (error) {
            logger.error('获取二维码列表失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取二维码列表失败'
            });
        }
    }
}

module.exports = new QRCodesController();
