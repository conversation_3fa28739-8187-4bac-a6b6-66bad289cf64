/**
 * 成品管理服务层
 * 处理成品相关的业务逻辑
 */

const databaseManager = require('../../../database/database');
const logger = require('../../../utils/logger');

class ProductsService {
    constructor() {
        this.db = databaseManager.getConnection();
    }

    /**
     * 获取成品列表
     */
    async getProducts(filters = {}) {
        try {
            let query = `
                SELECT p.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE 1=1
            `;
            
            const params = [];
            
            if (filters.product_type) {
                query += ' AND p.product_type = ?';
                params.push(filters.product_type);
            }
            
            if (filters.status) {
                query += ' AND p.status = ?';
                params.push(filters.status);
            }
            
            if (filters.search) {
                query += ' AND (p.product_name LIKE ? OR p.product_code LIKE ?)';
                params.push(`%${filters.search}%`, `%${filters.search}%`);
            }
            
            query += ' GROUP BY p.id ORDER BY p.created_at DESC';
            
            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }
            
            return this.db.prepare(query).all(params);
        } catch (error) {
            logger.error('获取成品列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取成品详情
     */
    async getProductById(id) {
        try {
            const product = this.db.prepare(`
                SELECT p.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE p.id = ?
                GROUP BY p.id
            `).get(id);
            
            if (!product) {
                throw new Error('成品不存在');
            }
            
            return product;
        } catch (error) {
            logger.error('获取成品详情失败:', error);
            throw error;
        }
    }

    /**
     * 创建成品
     */
    async createProduct(productData, operatorId) {
        try {
            const { product_code, product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description } = productData;
            
            // 检查成品编码是否已存在
            const existing = this.db.prepare('SELECT id FROM warehouse_finished_products WHERE product_code = ?').get(product_code);
            if (existing) {
                throw new Error('成品编码已存在');
            }
            
            const result = this.db.prepare(`
                INSERT INTO warehouse_finished_products (
                    product_code, product_name, product_type, unit, 
                    packaging_info, min_stock_level, max_stock_level, 
                    description, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, datetime('now'), datetime('now'))
            `).run(product_code, product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, operatorId);
            
            logger.info(`成品创建成功: ${product_code}`, { operatorId });
            return { id: result.lastInsertRowid, product_code };
        } catch (error) {
            logger.error('创建成品失败:', error);
            throw error;
        }
    }

    /**
     * 更新成品
     */
    async updateProduct(id, productData, operatorId) {
        try {
            const { product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, status } = productData;
            
            const result = this.db.prepare(`
                UPDATE warehouse_finished_products 
                SET product_name = ?, product_type = ?, unit = ?, packaging_info = ?, 
                    min_stock_level = ?, max_stock_level = ?, description = ?, status = ?, 
                    updated_by = ?, updated_at = datetime('now')
                WHERE id = ?
            `).run(product_name, product_type, unit, packaging_info, min_stock_level, max_stock_level, description, status, operatorId, id);
            
            if (result.changes === 0) {
                throw new Error('成品不存在或更新失败');
            }
            
            logger.info(`成品更新成功: ID ${id}`, { operatorId });
            return { id, updated: true };
        } catch (error) {
            logger.error('更新成品失败:', error);
            throw error;
        }
    }

    /**
     * 删除成品
     */
    async deleteProduct(id, operatorId) {
        try {
            // 检查是否有库存事务记录
            const hasTransactions = this.db.prepare('SELECT id FROM warehouse_inventory_transactions WHERE item_type = ? AND item_id = ? LIMIT 1').get('product', id);
            if (hasTransactions) {
                throw new Error('该成品存在库存事务记录，无法删除');
            }
            
            const result = this.db.prepare('DELETE FROM warehouse_finished_products WHERE id = ?').run(id);
            
            if (result.changes === 0) {
                throw new Error('成品不存在');
            }
            
            logger.info(`成品删除成功: ID ${id}`, { operatorId });
            return { id, deleted: true };
        } catch (error) {
            logger.error('删除成品失败:', error);
            throw error;
        }
    }

    /**
     * 成品入库
     */
    async productInbound(inboundData, operatorId) {
        try {
            const { product_id, quantity, batch_number, qrcode, notes } = inboundData;
            
            // 验证成品存在
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建入库事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'inbound',
                item_type: 'product',
                item_id: product_id,
                quantity,
                batch_number,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品入库成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品入库失败:', error);
            throw error;
        }
    }

    /**
     * 成品出库
     */
    async productOutbound(outboundData, operatorId) {
        try {
            const { product_id, quantity, qrcode, notes } = outboundData;
            
            // 验证成品存在和库存
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            if (product.current_stock < quantity) {
                throw new Error('库存不足');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建出库事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'outbound',
                item_type: 'product',
                item_id: product_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品出库成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品出库失败:', error);
            throw error;
        }
    }

    /**
     * 成品返仓
     */
    async productReturn(returnData, operatorId) {
        try {
            const { product_id, quantity, qrcode, notes } = returnData;
            
            // 验证成品存在
            const product = await this.getProductById(product_id);
            if (!product) {
                throw new Error('成品不存在');
            }
            
            // 验证二维码（如果提供）
            if (qrcode) {
                const QRCodeService = require('./qrcodesService');
                await QRCodeService.validateQRCode(qrcode, 'product', product_id);
            }
            
            // 创建返仓事务
            const TransactionService = require('./transactionsService');
            const transaction = await TransactionService.createTransaction({
                transaction_type: 'return',
                item_type: 'product',
                item_id: product_id,
                quantity,
                qrcode,
                notes,
                operator_id: operatorId
            });
            
            logger.info(`成品返仓成功: ${product.product_code}, 数量: ${quantity}`, { operatorId });
            return transaction;
        } catch (error) {
            logger.error('成品返仓失败:', error);
            throw error;
        }
    }

    /**
     * 获取成品库存报告
     */
    async getProductsInventory(filters = {}) {
        try {
            let query = `
                SELECT p.*, 
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) as total_inbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) as total_outbound,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as total_return,
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                       COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0) as current_stock,
                       CASE 
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) <= p.min_stock_level 
                           THEN 'low_stock'
                           WHEN (COALESCE(SUM(CASE WHEN t.transaction_type = 'inbound' THEN t.quantity ELSE 0 END), 0) -
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'outbound' THEN t.quantity ELSE 0 END), 0) +
                                 COALESCE(SUM(CASE WHEN t.transaction_type = 'return' THEN t.quantity ELSE 0 END), 0)) >= p.max_stock_level 
                           THEN 'high_stock'
                           ELSE 'normal'
                       END as stock_status
                FROM warehouse_finished_products p
                LEFT JOIN warehouse_inventory_transactions t ON p.id = t.item_id AND t.item_type = 'product'
                WHERE p.status = 'active'
            `;
            
            const params = [];
            
            if (filters.stock_status) {
                query += ' GROUP BY p.id HAVING stock_status = ?';
                params.push(filters.stock_status);
            } else {
                query += ' GROUP BY p.id';
            }
            
            query += ' ORDER BY p.product_name';
            
            return this.db.prepare(query).all(params);
        } catch (error) {
            logger.error('获取成品库存报告失败:', error);
            throw error;
        }
    }
}

module.exports = new ProductsService();
