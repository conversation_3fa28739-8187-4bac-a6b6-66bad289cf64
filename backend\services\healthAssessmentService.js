/**
 * 设备健康度评估服务
 * 实现四维度健康度计算算法
 */

const logger = require('../utils/logger');
const HealthCalculator = require('../utils/healthCalculator');
const FaultPredictor = require('../utils/faultPredictor');
const RecommendationEngine = require('../utils/recommendationEngine');

class HealthAssessmentService {
    constructor(database) {
        this.db = database;
        this.calculator = new HealthCalculator();
        this.predictor = new FaultPredictor();
        this.recommendationEngine = new RecommendationEngine();

        // 延迟初始化语句，等待迁移完成
        this.statements = null;
        this.initStatementsWhenReady();
    }

    /**
     * 等待迁移完成后初始化语句
     */
    async initStatementsWhenReady() {
        // 检查数据库管理器是否完成迁移
        const databaseManager = require('../database/database');

        // 等待迁移完成
        let attempts = 0;
        const maxAttempts = 50; // 最多等待5秒

        while (!databaseManager.migrationCompleted && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        // 初始化预编译语句
        this.initStatements();
    }

    /**
     * 初始化数据库预编译语句
     */
    initStatements() {
        if (!this.db) {
            logger.error('数据库连接未初始化');
            return;
        }

        try {
            this.statements = {
            // 设备健康度相关
            findLatestHealth: this.db.prepare(`
                SELECT * FROM equipment_health 
                WHERE equipment_id = ? 
                ORDER BY assessment_date DESC 
                LIMIT 1
            `),
            insertHealth: this.db.prepare(`
                INSERT INTO equipment_health (
                    id, equipment_id, total_score, health_level,
                    age_score, repair_frequency_score, fault_severity_score, maintenance_score,
                    assessment_date, assessor, calculation_details, recommendations,
                    next_maintenance_date, failure_probability, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            insertHealthHistory: this.db.prepare(`
                INSERT INTO equipment_health_history (
                    id, equipment_id, total_score, health_level,
                    age_score, repair_frequency_score, fault_severity_score, maintenance_score,
                    assessment_date, assessor, calculation_details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `),
            findHealthHistory: this.db.prepare(`
                SELECT * FROM equipment_health_history 
                WHERE equipment_id = ? 
                ORDER BY assessment_date DESC 
                LIMIT ?
            `),
            findEquipmentById: this.db.prepare(`
                SELECT * FROM equipment WHERE id = ?
            `),
            findMaintenanceRecords: this.db.prepare(`
                SELECT * FROM equipment_maintenance 
                WHERE equipment_id = ? 
                ORDER BY maintenance_date DESC
            `),
            findAllEquipmentHealth: this.db.prepare(`
                SELECT eh.*, e.name as equipment_name, e.code as equipment_code, e.area
                FROM equipment_health eh
                LEFT JOIN equipment e ON eh.equipment_id = e.id
                ORDER BY eh.assessment_date DESC
            `)
            };

            // 只在详细日志模式下显示初始化信息
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('健康度评估服务预编译语句初始化完成');
            }
        } catch (error) {
            logger.error('初始化健康度评估服务预编译语句失败:', error);
            throw error;
        }
    }

    /**
     * 计算设备健康度
     */
    async calculateEquipmentHealth(equipmentId, assessor) {
        try {
            // 确保预编译语句已初始化
            if (!this.statements) {
                await this.initStatementsWhenReady();
            }
            // 获取设备基本信息
            const equipment = this.statements.findEquipmentById.get(equipmentId);
            if (!equipment) {
                throw new Error('设备不存在');
            }

            // 获取维修记录
            const maintenanceRecords = this.statements.findMaintenanceRecords.all(equipmentId);

            // 计算四维度评分
            const dimensions = this.calculator.calculateAllDimensions(equipment, maintenanceRecords);

            // 计算总分和等级
            const totalScore = this.calculator.calculateTotalScore(dimensions);
            const healthLevel = this.calculator.getHealthLevel(totalScore);

            // 生成维护建议
            const recommendations = this.recommendationEngine.generateRecommendations(
                dimensions, equipment, maintenanceRecords
            );

            // 故障预测
            const prediction = this.predictor.predictNextFailure(maintenanceRecords);

            // 构建结果对象
            const healthData = {
                equipmentId,
                totalScore,
                healthLevel,
                assessmentDate: new Date().toISOString(),
                dimensions,
                recommendations,
                prediction: prediction.canPredict ? prediction : null
            };

            // 保存到数据库
            await this.saveHealthAssessment(healthData, assessor);

            logger.info('设备健康度计算完成', {
                equipmentId,
                totalScore,
                healthLevel,
                assessor
            });

            return healthData;
        } catch (error) {
            logger.error('设备健康度计算失败', {
                equipmentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 保存健康度评估结果
     */
    async saveHealthAssessment(healthData, assessor) {
        const now = new Date().toISOString();
        const healthId = `HEALTH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const historyId = `HIST_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        try {
            // 开始事务
            const transaction = this.db.transaction(() => {
                // 插入当前健康度记录
                this.statements.insertHealth.run(
                    healthId,
                    healthData.equipmentId,
                    healthData.totalScore,
                    healthData.healthLevel,
                    healthData.dimensions.age.score,
                    healthData.dimensions.repairFrequency.score,
                    healthData.dimensions.faultSeverity.score,
                    healthData.dimensions.maintenance.score,
                    healthData.assessmentDate,
                    assessor,
                    JSON.stringify(healthData.dimensions),
                    JSON.stringify(healthData.recommendations),
                    healthData.prediction?.predictedDate || null,
                    healthData.prediction?.confidence || 0,
                    now,
                    now
                );

                // 插入历史记录
                this.statements.insertHealthHistory.run(
                    historyId,
                    healthData.equipmentId,
                    healthData.totalScore,
                    healthData.healthLevel,
                    healthData.dimensions.age.score,
                    healthData.dimensions.repairFrequency.score,
                    healthData.dimensions.faultSeverity.score,
                    healthData.dimensions.maintenance.score,
                    healthData.assessmentDate,
                    assessor,
                    JSON.stringify(healthData.dimensions),
                    now
                );
            });

            transaction();
        } catch (error) {
            logger.error('保存健康度评估失败', {
                equipmentId: healthData.equipmentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取设备健康度历史
     */
    async getEquipmentHealthHistory(equipmentId, limit = 30) {
        try {
            const history = this.statements.findHealthHistory.all(equipmentId, limit);
            
            // 计算趋势
            const trends = this.calculateTrends(history);

            return {
                equipmentId,
                history: history.map(record => ({
                    ...record,
                    calculation_details: JSON.parse(record.calculation_details || '{}')
                })),
                trends
            };
        } catch (error) {
            logger.error('获取健康度历史失败', {
                equipmentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 计算健康度趋势
     */
    calculateTrends(history) {
        if (history.length < 2) {
            return null;
        }

        const current = history[0];
        const previous = history[1];

        return {
            totalScore: {
                current: current.total_score,
                previous: previous.total_score,
                change: current.total_score - previous.total_score,
                trend: current.total_score > previous.total_score ? 'increasing' : 
                       current.total_score < previous.total_score ? 'decreasing' : 'stable'
            }
        };
    }

    /**
     * 获取所有设备健康度统计
     */
    async getHealthStatistics(filters = {}) {
        try {
            const allHealth = this.statements.findAllEquipmentHealth.all();
            
            // 按等级分组统计
            const distribution = {
                excellent: 0,
                good: 0,
                average: 0,
                poor: 0,
                dangerous: 0
            };

            let totalScore = 0;
            const warningEquipment = [];

            allHealth.forEach(record => {
                totalScore += record.total_score;
                
                // 统计分布
                switch (record.health_level) {
                    case '优秀': distribution.excellent++; break;
                    case '良好': distribution.good++; break;
                    case '一般': distribution.average++; break;
                    case '较差': distribution.poor++; break;
                    case '危险': distribution.dangerous++; break;
                }

                // 收集需要关注的设备
                if (record.total_score < 70) {
                    warningEquipment.push({
                        equipmentId: record.equipment_id,
                        equipmentName: record.equipment_name,
                        equipmentCode: record.equipment_code,
                        area: record.area,
                        totalScore: record.total_score,
                        level: record.health_level,
                        urgentRecommendations: JSON.parse(record.recommendations || '[]')
                            .filter(r => r.priority === 'urgent' || r.priority === 'high').length
                    });
                }
            });

            return {
                overview: {
                    totalEquipment: allHealth.length,
                    averageHealth: allHealth.length > 0 ? Math.round(totalScore / allHealth.length) : 0,
                    lastUpdated: new Date().toISOString()
                },
                distribution,
                warningEquipment: warningEquipment.sort((a, b) => a.totalScore - b.totalScore)
            };
        } catch (error) {
            logger.error('获取健康度统计失败', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 批量计算设备健康度
     */
    async batchCalculateHealth(equipmentIds, assessor, options = {}) {
        const results = [];
        const errors = [];

        for (const equipmentId of equipmentIds) {
            try {
                const result = await this.calculateEquipmentHealth(equipmentId, assessor);
                results.push({
                    equipmentId,
                    totalScore: result.totalScore,
                    level: result.healthLevel,
                    calculatedAt: result.assessmentDate,
                    success: true
                });
            } catch (error) {
                errors.push({
                    equipmentId,
                    error: error.message,
                    success: false
                });
            }
        }

        return {
            results: [...results, ...errors],
            summary: {
                total: equipmentIds.length,
                successful: results.length,
                failed: errors.length,
                averageScore: results.length > 0 ? 
                    Math.round(results.reduce((sum, r) => sum + r.totalScore, 0) / results.length) : 0
            }
        };
    }
}

module.exports = HealthAssessmentService;
