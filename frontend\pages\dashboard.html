<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
    <link rel="stylesheet" href="/assets/css/dashboard.css">

</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 侧边导航栏 -->
        <sidebar
            :user="currentUser"
            :sidebar-open="sidebarOpen">
        </sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 md:ml-72">


            <!-- 主内容 -->
            <main class="p-4 lg:p-6 space-y-6">
                <!-- 欢迎区域 -->
                <div class="dashboard-hero rounded-2xl p-6 lg:p-8 text-white relative">
                    <div class="relative z-10">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div class="mb-4 lg:mb-0">
                                <h1 class="text-2xl lg:text-3xl font-bold mb-2">
                                    {{ getGreeting() }}，{{ currentUser?.username || '用户' }}！
                                </h1>
                                <p class="text-blue-100 text-sm lg:text-base">
                                    {{ formatDate(new Date()) }} · 让我们开始今天的工作
                                </p>
                            </div>
                            <div class="hidden lg:flex items-center space-x-4">
                                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <button @click="handleRefresh" class="bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors rounded-lg p-3">
                                    <svg class="w-5 h-5 text-white" :class="{ 'animate-spin': refreshing }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计数据面板 -->
                <div class="stats-grid">
                    <!-- 申请管理统计 -->
                    <div class="stat-card blue">
                        <div class="stat-header">
                            <div class="stat-icon bg-blue-100 text-blue-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <a href="/application-record" class="text-blue-600 hover:text-blue-800 text-xs font-medium transition-colors">
                                查看详情 →
                            </a>
                        </div>
                        <div class="stat-value">{{ stats.applications?.total || 0 }}</div>
                        <div class="stat-label">申请总数</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">待审核: {{ stats.applications?.pending || 0 }}</span>
                            <span class="stat-change positive">
                                {{ stats.applications?.approvalRate || 0 }}% 通过率
                            </span>
                        </div>
                    </div>

                    <!-- 设备管理统计 -->
                    <div class="stat-card green">
                        <div class="stat-header">
                            <div class="stat-icon bg-green-100 text-green-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <a href="/equipment/info" class="text-green-600 hover:text-green-800 text-xs font-medium transition-colors">
                                查看详情 →
                            </a>
                        </div>
                        <div class="stat-value">{{ stats.equipment?.total || 0 }}</div>
                        <div class="stat-label">设备总数</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">运行中: {{ stats.equipment?.active || 0 }}</span>
                            <span class="stat-change" :class="(stats.equipment?.healthRate || 0) >= 80 ? 'positive' : 'negative'">
                                {{ stats.equipment?.healthRate || 0 }}% 健康率
                            </span>
                        </div>
                    </div>

                    <!-- 质量管理统计 -->
                    <div class="stat-card purple">
                        <div class="stat-header">
                            <div class="stat-icon bg-purple-100 text-purple-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <a href="/quality-list" class="text-purple-600 hover:text-purple-800 text-xs font-medium transition-colors">
                                查看详情 →
                            </a>
                        </div>
                        <div class="stat-value">{{ stats.quality?.total || 0 }}</div>
                        <div class="stat-label">质量报告</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">本月: {{ stats.quality?.thisMonth || 0 }}</span>
                            <span class="stat-change positive">
                                {{ stats.quality?.passRate || 0 }}% 合格率
                            </span>
                        </div>
                    </div>

                    <!-- 排程管理统计 -->
                    <div class="stat-card orange">
                        <div class="stat-header">
                            <div class="stat-icon bg-orange-100 text-orange-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m0 0V7a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h8z"></path>
                                </svg>
                            </div>
                            <a href="/schedule/list" class="text-orange-600 hover:text-orange-800 text-xs font-medium transition-colors">
                                查看详情 →
                            </a>
                        </div>
                        <div class="stat-value">{{ stats.schedules?.total || 0 }}</div>
                        <div class="stat-label">排程计划</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">进行中: {{ stats.schedules?.inProgress || 0 }}</span>
                            <span class="stat-change positive">
                                {{ stats.schedules?.completionRate || 0 }}% 完成率
                            </span>
                        </div>
                    </div>

                    <!-- 用户统计 -->
                    <div class="stat-card indigo">
                        <div class="stat-header">
                            <div class="stat-icon bg-indigo-100 text-indigo-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                </svg>
                            </div>
                            <a href="/user-management" class="text-indigo-600 hover:text-indigo-800 text-xs font-medium transition-colors" v-if="hasPermission('view_users')">
                                查看详情 →
                            </a>
                        </div>
                        <div class="stat-value">{{ stats.users?.total || 0 }}</div>
                        <div class="stat-label">系统用户</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">在线: {{ stats.users?.onlineUsers || 0 }}</span>
                            <span class="text-gray-600 text-xs">今日: {{ stats.users?.todayLogin || 0 }}</span>
                        </div>
                    </div>

                    <!-- 系统状态 -->
                    <div class="stat-card red">
                        <div class="stat-header">
                            <div class="stat-icon bg-red-100 text-red-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="text-xs font-medium" :class="systemHealthColor">{{ systemHealthText }}</span>
                        </div>
                        <div class="stat-value">{{ currentUptime || '0s' }}</div>
                        <div class="stat-label">系统运行时间</div>
                        <div class="stat-footer">
                            <span class="text-gray-600 text-xs">内存: {{ stats.system?.memoryUsage || 0 }}%</span>
                            <span class="text-gray-600 text-xs">CPU: {{ stats.system?.cpuUsage || 0 }}%</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <div class="section-header">
                        <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <h3 class="section-title">快速操作</h3>
                    </div>
                    <div class="actions-grid">
                        <a v-for="action in quickActions" :key="action.title" :href="action.url" class="action-card">
                            <div class="action-icon" :class="`bg-${action.color}-100 text-${action.color}-600`">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" v-html="getActionIcon(action.icon)">
                                </svg>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-1 text-sm">{{ action.title }}</h4>
                            <p class="text-xs text-gray-600 leading-relaxed">{{ action.description }}</p>
                        </a>
                    </div>
                </div>

                <!-- 下方内容区域 -->
                <div class="content-grid">
                    <!-- 待处理任务 -->
                    <div class="content-card">
                        <div class="section-header">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                            <h3 class="section-title">待处理任务</h3>
                        </div>
                        <div class="card-content">
                            <div v-if="pendingTasks.length === 0" class="empty-state">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p>暂无待处理任务</p>
                            </div>
                            <div v-else class="scrollable-content">
                                <div class="item-list">
                                    <a v-for="task in pendingTasks" :key="task.type" :href="task.url" class="list-item">
                                        <div class="priority-dot" :class="getPriorityColor(task.priority)"></div>
                                        <div class="item-content">
                                            <div class="item-title">{{ task.title }}</div>
                                            <div class="item-subtitle">{{ task.count }} 项待处理</div>
                                        </div>
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="content-card">
                        <div class="section-header">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="section-title">最近活动</h3>
                        </div>
                        <div class="card-content">
                            <div v-if="activities.length === 0" class="empty-state">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p>暂无活动记录</p>
                            </div>
                            <div v-else class="scrollable-content">
                                <div class="item-list">
                                    <div v-for="activity in activities" :key="activity.id" class="list-item">
                                        <div class="item-icon" :class="getActivityIconClass(activity.type)">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" v-html="getActivityIcon(activity.type)">
                                            </svg>
                                        </div>
                                        <div class="item-content">
                                            <div class="item-title">{{ activity.title || '未知活动' }}</div>
                                            <div class="item-subtitle">
                                                <span class="font-medium">{{ activity.user || '未知用户' }}</span>
                                                <span class="mx-1">·</span>
                                                <span>{{ formatTime(activity.time) }}</span>
                                                <span v-if="activity.description" class="mx-1">·</span>
                                                <span v-if="activity.description" class="text-gray-500">{{ activity.description }}</span>
                                            </div>
                                        </div>
                                        <div class="item-badge" :class="getStatusClass(activity.status)">
                                            {{ getStatusText(activity.status) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通知中心 -->
                    <div class="content-card">
                        <div class="section-header">
                            <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"></path>
                            </svg>
                            <h3 class="section-title">通知中心</h3>
                        </div>
                        <div class="card-content">
                            <div v-if="notifications.length === 0" class="empty-state">
                                <p>暂无通知</p>
                            </div>
                            <div v-else class="scrollable-content">
                                <div v-for="notification in notifications" :key="notification.type"
                                     class="notification-item" :class="notification.priority">
                                    <div class="flex-1">
                                        <div class="item-title">{{ notification.title }}</div>
                                        <div class="item-subtitle mb-1">{{ notification.message }}</div>
                                        <div class="text-xs text-gray-400">{{ formatTime(notification.time) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div v-if="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"></div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js?v=20250731-2"></script>
    <script type="module" src="/scripts/pages/dashboard.js?v=20250731-2"></script>
</body>
</html>
