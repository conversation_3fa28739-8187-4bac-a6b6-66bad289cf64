/**
 * 全局初始化脚本
 * 在所有页面加载时执行的通用初始化逻辑
 */

import { initializeAPI, isAuthenticated, redirectToLogin } from './api/config.js';
import configManager from './configManager.js';

/**
 * 检查页面是否需要认证
 * @param {string} pathname - 当前页面路径
 * @returns {boolean} 是否需要认证
 */
function requiresAuth(pathname) {
    // 不需要认证的页面列表
    const publicPages = [
        '/',
        '/login',
        '/pages/login.html',
        '/pages/index.html'
    ];

    // dashboard页面需要认证，但由其自身的JavaScript处理认证逻辑
    if (pathname === '/dashboard') {
        return false; // 让dashboard页面自己处理认证
    }

    return !publicPages.includes(pathname);
}

/**
 * 检查用户认证状态
 */
function checkAuthStatus() {
    const currentPath = window.location.pathname;
    
    // 如果当前页面需要认证但用户未登录
    if (requiresAuth(currentPath) && !isAuthenticated()) {
        console.warn('页面需要认证，但用户未登录，重定向到登录页面');
        redirectToLogin('请先登录后再访问此页面');
        return false;
    }
    
    return true;
}

/**
 * 处理登录成功后的重定向
 */
function handleLoginRedirect() {
    const redirectPath = sessionStorage.getItem('redirectAfterLogin');
    if (redirectPath) {
        sessionStorage.removeItem('redirectAfterLogin');
        window.location.href = redirectPath;
    }
}

/**
 * 全局错误处理
 */
function setupGlobalErrorHandling() {
    // 处理未捕获的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未处理的Promise错误:', event.reason);
        
        // 如果是网络错误，显示友好提示
        if (event.reason && event.reason.message && event.reason.message.includes('Network Error')) {
            showNotification('网络连接失败，请检查网络设置', 'error');
        }
    });

    // 处理JavaScript错误
    window.addEventListener('error', (event) => {
        console.error('JavaScript错误:', event.error);
    });
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, warning, info)
 * @param {number} duration - 显示时长(毫秒)
 */
function showNotification(message, type = 'info', duration = 3000) {
    const colors = {
        success: 'bg-green-100 border-green-400 text-green-700',
        error: 'bg-red-100 border-red-400 text-red-700',
        warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
        info: 'bg-blue-100 border-blue-400 text-blue-700'
    };

    const icons = {
        success: `<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>`,
        error: `<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>`,
        warning: `<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>`,
        info: `<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>`
    };

    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 ${colors[type]} border px-4 py-3 rounded z-50 max-w-sm shadow-lg`;
    notification.innerHTML = `
        <div class="flex items-center">
            ${icons[type]}
            <span class="ml-2">${message}</span>
            <button class="ml-4 text-current opacity-70 hover:opacity-100" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

/**
 * 初始化应用
 */
function initializeApp() {
    // 从configManager获取环境信息
    const isDevelopment = configManager.isDevelopment;

    if (isDevelopment) {
        console.log('初始化全局应用配置...');
    }

    // 初始化API配置和拦截器
    initializeAPI();

    // 设置全局错误处理
    setupGlobalErrorHandling();

    // 检查认证状态
    checkAuthStatus();

    // 如果在登录页面且已登录，处理重定向
    if (window.location.pathname.includes('login') && isAuthenticated()) {
        handleLoginRedirect();
    }

    if (isDevelopment) {
        console.log('全局应用配置初始化完成');
    }
}

// 导出工具函数供其他模块使用
window.showNotification = showNotification;

// 当DOM加载完成时初始化应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}
