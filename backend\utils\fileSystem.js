/**
 * 文件系统工具
 * 处理文件读写和目录创建
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('./logger');

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

/**
 * 初始化所有必要的目录
 */
function initDirectories() {
    // 只在详细日志模式下显示目录初始化信息
    if (process.env.VERBOSE_LOGS === 'true') {
        logger.info('初始化系统目录');
    }

    // 确保数据目录存在
    ensureDirectoryExists(config.paths.data);

    // 确保上传目录存在
    ensureDirectoryExists(config.paths.uploads);

    // 确保质量报告目录存在
    const qualityReportsDir = path.join(config.paths.uploads, 'quality-reports');
    ensureDirectoryExists(qualityReportsDir);

    // 确保用户数据目录存在
    ensureDirectoryExists(config.paths.users);

    // 确保申请数据目录存在
    ensureDirectoryExists(config.paths.applications);

    // 确保索引目录存在
    ensureDirectoryExists(config.paths.indexes);

    // 确保日志目录存在
    ensureDirectoryExists(config.paths.logs);

    // 只在详细日志模式下显示完成信息
    if (process.env.VERBOSE_LOGS === 'true') {
        logger.info('系统目录初始化完成');
    }
}

/**
 * 读取JSON文件
 * @param {string} filePath - 文件路径
 * @param {any} defaultValue - 如果文件不存在，返回的默认值
 * @returns {any} 解析后的JSON数据
 */
function readJsonFile(filePath, defaultValue = []) {
    try {
        if (!fs.existsSync(filePath)) {
            logger.debug(`文件不存在，返回默认值: ${filePath}`);
            return defaultValue;
        }
        // 明确指定UTF-8编码读取文件
        const data = fs.readFileSync(filePath, {encoding: 'utf8'});
        const result = JSON.parse(data || JSON.stringify(defaultValue));
        logger.debug(`成功读取文件: ${filePath}`);
        return result;
    } catch (error) {
        logger.error(`读取文件失败: ${filePath}`, { error: error.message, stack: error.stack });
        return defaultValue;
    }
}

/**
 * 写入JSON文件
 * @param {string} filePath - 文件路径
 * @param {any} data - 要写入的数据
 * @returns {boolean} 是否成功
 */
function writeJsonFile(filePath, data) {
    try {
        const dirPath = path.dirname(filePath);
        ensureDirectoryExists(dirPath);
        // 确保使用UTF-8编码写入文件，并添加BOM标记以确保中文正确显示
        const jsonString = JSON.stringify(data, null, 2);
        fs.writeFileSync(filePath, jsonString, {encoding: 'utf8'});
        logger.debug(`成功写入文件: ${filePath}`);
        return true;
    } catch (error) {
        logger.error(`写入文件失败: ${filePath}`, { error: error.message, stack: error.stack });
        return false;
    }
}

/**
 * 删除文件
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否成功
 */
function deleteFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logger.debug(`成功删除文件: ${filePath}`);
        } else {
            logger.debug(`文件不存在，无需删除: ${filePath}`);
        }
        return true;
    } catch (error) {
        logger.error(`删除文件失败: ${filePath}`, { error: error.message, stack: error.stack });
        return false;
    }
}

module.exports = {
    ensureDirectoryExists,
    initDirectories,
    readJsonFile,
    writeJsonFile,
    deleteFile
};
