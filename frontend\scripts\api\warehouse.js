/**
 * 仓库管理API接口封装
 * 封装所有仓库管理相关的API调用
 */

import { API_URL, getAuthHeaders } from './config.js';

// 基础API路径
const BASE_URL = `${API_URL}/warehouse`;

// ==================== 物料管理 API ====================

/**
 * 获取物料列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getMaterials(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/materials`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取物料列表失败:', error);
        throw error;
    }
}

/**
 * 创建物料
 * @param {Object} materialData - 物料数据
 * @returns {Promise} API响应
 */
export async function createMaterial(materialData) {
    try {
        const response = await axios.post(`${BASE_URL}/materials`, materialData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('创建物料失败:', error);
        throw error;
    }
}

/**
 * 更新物料
 * @param {string} id - 物料ID
 * @param {Object} materialData - 物料数据
 * @returns {Promise} API响应
 */
export async function updateMaterial(id, materialData) {
    try {
        const response = await axios.put(`${BASE_URL}/materials/${id}`, materialData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新物料失败:', error);
        throw error;
    }
}

/**
 * 删除物料
 * @param {string} id - 物料ID
 * @returns {Promise} API响应
 */
export async function deleteMaterial(id) {
    try {
        const response = await axios.delete(`${BASE_URL}/materials/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除物料失败:', error);
        throw error;
    }
}

/**
 * 物料入库
 * @param {Object} inboundData - 入库数据
 * @returns {Promise} API响应
 */
export async function materialInbound(inboundData) {
    try {
        const response = await axios.post(`${BASE_URL}/materials/inbound`, inboundData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('物料入库失败:', error);
        throw error;
    }
}

/**
 * 物料发料
 * @param {Object} outboundData - 发料数据
 * @returns {Promise} API响应
 */
export async function materialOutbound(outboundData) {
    try {
        const response = await axios.post(`${BASE_URL}/materials/outbound`, outboundData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('物料发料失败:', error);
        throw error;
    }
}

/**
 * 物料退料
 * @param {Object} returnData - 退料数据
 * @returns {Promise} API响应
 */
export async function materialReturn(returnData) {
    try {
        const response = await axios.post(`${BASE_URL}/materials/return`, returnData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('物料退料失败:', error);
        throw error;
    }
}

// ==================== 成品管理 API ====================

/**
 * 获取成品列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getProducts(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/products`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取成品列表失败:', error);
        throw error;
    }
}

/**
 * 创建成品
 * @param {Object} productData - 成品数据
 * @returns {Promise} API响应
 */
export async function createProduct(productData) {
    try {
        const response = await axios.post(`${BASE_URL}/products`, productData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('创建成品失败:', error);
        throw error;
    }
}

/**
 * 更新成品
 * @param {string} id - 成品ID
 * @param {Object} productData - 成品数据
 * @returns {Promise} API响应
 */
export async function updateProduct(id, productData) {
    try {
        const response = await axios.put(`${BASE_URL}/products/${id}`, productData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新成品失败:', error);
        throw error;
    }
}

/**
 * 删除成品
 * @param {string} id - 成品ID
 * @returns {Promise} API响应
 */
export async function deleteProduct(id) {
    try {
        const response = await axios.delete(`${BASE_URL}/products/${id}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('删除成品失败:', error);
        throw error;
    }
}

/**
 * 成品入库
 * @param {Object} inboundData - 入库数据
 * @returns {Promise} API响应
 */
export async function productInbound(inboundData) {
    try {
        const response = await axios.post(`${BASE_URL}/products/inbound`, inboundData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('成品入库失败:', error);
        throw error;
    }
}

/**
 * 成品出库
 * @param {Object} outboundData - 出库数据
 * @returns {Promise} API响应
 */
export async function productOutbound(outboundData) {
    try {
        const response = await axios.post(`${BASE_URL}/products/outbound`, outboundData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('成品出库失败:', error);
        throw error;
    }
}

/**
 * 成品返仓
 * @param {Object} returnData - 返仓数据
 * @returns {Promise} API响应
 */
export async function productReturn(returnData) {
    try {
        const response = await axios.post(`${BASE_URL}/products/return`, returnData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('成品返仓失败:', error);
        throw error;
    }
}

// ==================== 库存事务 API ====================

/**
 * 获取库存事务记录
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getTransactions(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/transactions`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取库存事务记录失败:', error);
        throw error;
    }
}

/**
 * 获取库存统计
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getInventoryStats(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/transactions/stats`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取库存统计失败:', error);
        throw error;
    }
}

/**
 * 获取库存预警
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getInventoryAlerts(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/transactions/alerts`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取库存预警失败:', error);
        throw error;
    }
}

// ==================== 二维码管理 API ====================

/**
 * 生成二维码
 * @param {Object} qrData - 二维码数据
 * @returns {Promise} API响应
 */
export async function generateQRCode(qrData) {
    try {
        const response = await axios.post(`${BASE_URL}/qrcodes/generate`, qrData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('生成二维码失败:', error);
        throw error;
    }
}

/**
 * 批量生成二维码
 * @param {Object} batchData - 批量生成数据
 * @returns {Promise} API响应
 */
export async function batchGenerateQRCodes(batchData) {
    try {
        const response = await axios.post(`${BASE_URL}/qrcodes/batch-generate`, batchData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('批量生成二维码失败:', error);
        throw error;
    }
}

/**
 * 验证二维码
 * @param {string} qrCode - 二维码内容
 * @returns {Promise} API响应
 */
export async function validateQRCode(qrCode) {
    try {
        const response = await axios.post(`${BASE_URL}/qrcodes/validate`, { qr_code: qrCode }, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('验证二维码失败:', error);
        throw error;
    }
}

/**
 * 获取二维码信息
 * @param {string} qrCode - 二维码内容
 * @returns {Promise} API响应
 */
export async function getQRCodeInfo(qrCode) {
    try {
        const response = await axios.get(`${BASE_URL}/qrcodes/info/${encodeURIComponent(qrCode)}`, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取二维码信息失败:', error);
        throw error;
    }
}

/**
 * 更新二维码状态
 * @param {string} qrCode - 二维码内容
 * @param {Object} statusData - 状态数据
 * @returns {Promise} API响应
 */
export async function updateQRCodeStatus(qrCode, statusData) {
    try {
        const response = await axios.put(`${BASE_URL}/qrcodes/status/${encodeURIComponent(qrCode)}`, statusData, {
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('更新二维码状态失败:', error);
        throw error;
    }
}

// ==================== 报表查询 API ====================

/**
 * 获取库存报表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getInventoryReport(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/reports/inventory`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取库存报表失败:', error);
        throw error;
    }
}

/**
 * 获取事务报表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getTransactionReport(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/reports/transactions`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取事务报表失败:', error);
        throw error;
    }
}

/**
 * 获取追溯信息
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function getTraceabilityInfo(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/reports/traceability`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('获取追溯信息失败:', error);
        throw error;
    }
}

/**
 * 导出库存报表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function exportInventoryReport(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/reports/inventory/export`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('导出库存报表失败:', error);
        throw error;
    }
}

/**
 * 导出事务报表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export async function exportTransactionReport(params = {}) {
    try {
        const response = await axios.get(`${BASE_URL}/reports/transactions/export`, {
            params,
            headers: getAuthHeaders()
        });
        return response.data;
    } catch (error) {
        console.error('导出事务报表失败:', error);
        throw error;
    }
}

// ==================== 工具函数 ====================

/**
 * 格式化日期时间
 * @param {string|Date} dateTime - 日期时间
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 获取操作类型文本
 * @param {string} type - 操作类型
 * @returns {string} 操作类型文本
 */
export function getOperationTypeText(type) {
    const typeMap = {
        'inbound': '入库',
        'outbound': '出库',
        'return': '退库',
        'transfer': '调拨',
        'adjust': '调整',
        'check': '盘点'
    };
    return typeMap[type] || type;
}

/**
 * 获取库存状态文本
 * @param {string} status - 库存状态
 * @returns {string} 状态文本
 */
export function getStockStatus(status) {
    const statusMap = {
        'normal': '正常',
        'low': '库存不足',
        'out': '缺货',
        'excess': '库存过多'
    };
    return statusMap[status] || status;
}

/**
 * 获取报表类型列表
 * @returns {Array} 报表类型列表
 */
export function getReports() {
    return [
        { id: 'inventory', name: '库存报表', description: '当前库存状态统计' },
        { id: 'transactions', name: '事务报表', description: '库存变动记录统计' },
        { id: 'materials', name: '物料报表', description: '物料使用情况统计' },
        { id: 'products', name: '成品报表', description: '成品库存情况统计' }
    ];
}
