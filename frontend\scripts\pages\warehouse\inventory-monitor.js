/**
 * 仓库管理 - 库存监控页面
 * 提供库存总览、实时监控和预警功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import { 
    getMaterials, getProducts, getTransactions, getInventoryStats,
    getStockStatus, formatDateTime, getOperationTypeText
} from '../../api/warehouse.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    requiredPermissions: ['warehouse_view'], // 需要仓库查看权限
    components: {
        Sidebar
    },
    setup() {
        const { ref, reactive, onMounted, onUnmounted, watch, computed } = Vue;

        // 权限检查
        const hasPermission = (permission) => {
            const user = window.currentUser;
            return user && user.permissions && user.permissions.includes(permission);
        };

        // 权限计算属性
        const canManage = computed(() => hasPermission('warehouse_manage'));

        // 页面状态
        const activeTab = ref('overview'); // 'overview' | 'monitor' | 'alerts'
        const isLoadingTransactions = ref(false);
        const isLoadingMonitor = ref(false);
        const isLoadingAlerts = ref(false);
        const autoRefresh = ref(false);
        const refreshInterval = ref(null);

        // 数据状态
        const stats = reactive({
            totalMaterials: 0,
            totalProducts: 0,
            totalTransactions: 0,
            lowStockItems: 0
        });
        const topMaterials = ref([]);
        const topProducts = ref([]);
        const recentTransactions = ref([]);
        const monitorItems = ref([]);
        const alerts = ref([]);
        const alertsCount = computed(() => alerts.value.length);

        // 监控筛选
        const monitorFilter = reactive({
            type: '',
            status: '',
            search: ''
        });
        const monitorCurrentPage = ref(1);
        const monitorItemsPerPage = ref(20);

        // 详情模态框
        const showDetailsModal = ref(false);
        const selectedItemDetails = ref(null);
        const itemTransactions = ref([]);

        // 计算属性
        const filteredMonitorItems = computed(() => {
            let filtered = [...monitorItems.value];

            // 类型筛选
            if (monitorFilter.type) {
                filtered = filtered.filter(item => item.type === monitorFilter.type);
            }

            // 状态筛选
            if (monitorFilter.status) {
                filtered = filtered.filter(item => {
                    const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
                    return status === monitorFilter.status;
                });
            }

            // 搜索筛选
            if (monitorFilter.search) {
                const term = monitorFilter.search.toLowerCase();
                filtered = filtered.filter(item =>
                    (item.code && item.code.toLowerCase().includes(term)) ||
                    (item.name && item.name.toLowerCase().includes(term))
                );
            }

            return filtered;
        });

        const paginatedMonitorItems = computed(() => {
            const start = (monitorCurrentPage.value - 1) * monitorItemsPerPage.value;
            const end = start + monitorItemsPerPage.value;
            return filteredMonitorItems.value.slice(start, end);
        });

        const monitorTotalPages = computed(() => 
            Math.ceil(filteredMonitorItems.value.length / monitorItemsPerPage.value)
        );

        // 监听器
        watch(activeTab, (newTab) => {
            if (newTab === 'overview') {
                loadOverviewData();
            } else if (newTab === 'monitor') {
                loadMonitorData();
            } else if (newTab === 'alerts') {
                loadAlerts();
            }
        });

        watch(autoRefresh, (enabled) => {
            if (enabled) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        // 页面初始化
        onMounted(() => {
            loadOverviewData();
        });

        onUnmounted(() => {
            stopAutoRefresh();
        });

        // 自动刷新控制
        function startAutoRefresh() {
            if (refreshInterval.value) {
                clearInterval(refreshInterval.value);
            }
            refreshInterval.value = setInterval(() => {
                if (activeTab.value === 'monitor') {
                    loadMonitorData();
                } else if (activeTab.value === 'alerts') {
                    loadAlerts();
                }
            }, 30000); // 30秒刷新一次
        }

        function stopAutoRefresh() {
            if (refreshInterval.value) {
                clearInterval(refreshInterval.value);
                refreshInterval.value = null;
            }
        }

        // 加载总览数据
        async function loadOverviewData() {
            try {
                // 并行加载所有数据
                const [materialsRes, productsRes, transactionsRes] = await Promise.all([
                    getMaterials(),
                    getProducts(),
                    getTransactions({ limit: 10, sort: 'created_at', order: 'desc' })
                ]);

                // 处理物料数据
                if (materialsRes.success) {
                    const materials = materialsRes.data || [];
                    stats.totalMaterials = materials.length;
                    topMaterials.value = materials
                        .sort((a, b) => (b.current_stock || 0) - (a.current_stock || 0))
                        .slice(0, 10);
                }

                // 处理成品数据
                if (productsRes.success) {
                    const products = productsRes.data || [];
                    stats.totalProducts = products.length;
                    topProducts.value = products
                        .sort((a, b) => (b.current_stock || 0) - (a.current_stock || 0))
                        .slice(0, 10);
                }

                // 处理事务数据
                if (transactionsRes.success) {
                    const transactions = transactionsRes.data || [];
                    recentTransactions.value = transactions;
                    
                    // 计算今日事务数量
                    const today = new Date().toISOString().split('T')[0];
                    stats.totalTransactions = transactions.filter(t => 
                        t.created_at && t.created_at.startsWith(today)
                    ).length;
                }

                // 计算低库存项目
                const allItems = [...(materialsRes.data || []), ...(productsRes.data || [])];
                stats.lowStockItems = allItems.filter(item => {
                    const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
                    return status === 'low' || status === 'critical' || status === 'out';
                }).length;

            } catch (error) {
                console.error('加载总览数据失败:', error);
            }
        }

        // 加载监控数据
        async function loadMonitorData() {
            try {
                isLoadingMonitor.value = true;
                
                const [materialsRes, productsRes] = await Promise.all([
                    getMaterials(),
                    getProducts()
                ]);

                const allItems = [];

                // 处理物料数据
                if (materialsRes.success) {
                    const materials = (materialsRes.data || []).map(item => ({
                        ...item,
                        type: 'material'
                    }));
                    allItems.push(...materials);
                }

                // 处理成品数据
                if (productsRes.success) {
                    const products = (productsRes.data || []).map(item => ({
                        ...item,
                        type: 'finished_product'
                    }));
                    allItems.push(...products);
                }

                monitorItems.value = allItems.sort((a, b) => {
                    // 按库存状态排序，优先显示有问题的
                    const statusA = getStockStatus(a.current_stock || 0, a.min_stock || 0, a.max_stock || 0);
                    const statusB = getStockStatus(b.current_stock || 0, b.min_stock || 0, b.max_stock || 0);
                    
                    const statusOrder = { 'out': 0, 'critical': 1, 'low': 2, 'normal': 3 };
                    return (statusOrder[statusA] || 3) - (statusOrder[statusB] || 3);
                });

            } catch (error) {
                console.error('加载监控数据失败:', error);
            } finally {
                isLoadingMonitor.value = false;
            }
        }

        // 加载预警数据
        async function loadAlerts() {
            try {
                isLoadingAlerts.value = true;
                
                const [materialsRes, productsRes] = await Promise.all([
                    getMaterials(),
                    getProducts()
                ]);

                const alertItems = [];

                // 检查物料预警
                if (materialsRes.success) {
                    const materials = materialsRes.data || [];
                    materials.forEach(item => {
                        const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
                        if (status !== 'normal') {
                            alertItems.push({
                                ...item,
                                type: 'material',
                                alert_level: status === 'out' ? 'critical' : status === 'critical' ? 'critical' : 'warning',
                                alert_message: getAlertMessage(status, item.current_stock || 0, item.min_stock || 0),
                                created_at: new Date().toISOString()
                            });
                        }
                    });
                }

                // 检查成品预警
                if (productsRes.success) {
                    const products = productsRes.data || [];
                    products.forEach(item => {
                        const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
                        if (status !== 'normal') {
                            alertItems.push({
                                ...item,
                                type: 'finished_product',
                                alert_level: status === 'out' ? 'critical' : status === 'critical' ? 'critical' : 'warning',
                                alert_message: getAlertMessage(status, item.current_stock || 0, item.min_stock || 0),
                                created_at: new Date().toISOString()
                            });
                        }
                    });
                }

                // 按严重程度排序
                alerts.value = alertItems.sort((a, b) => {
                    const levelOrder = { 'critical': 0, 'warning': 1, 'info': 2 };
                    return (levelOrder[a.alert_level] || 2) - (levelOrder[b.alert_level] || 2);
                });

            } catch (error) {
                console.error('加载预警数据失败:', error);
            } finally {
                isLoadingAlerts.value = false;
            }
        }

        // 获取预警消息
        function getAlertMessage(status, currentStock, minStock) {
            switch (status) {
                case 'out':
                    return '库存已耗尽，请立即补货';
                case 'critical':
                    return `库存严重不足，当前库存 ${currentStock}，低于最小库存 ${minStock}`;
                case 'low':
                    return `库存偏低，当前库存 ${currentStock}，接近最小库存 ${minStock}`;
                default:
                    return '库存状态异常';
            }
        }

        // 刷新方法
        function refreshMonitorData() {
            if (activeTab.value === 'monitor') {
                loadMonitorData();
            } else if (activeTab.value === 'alerts') {
                loadAlerts();
            } else {
                loadOverviewData();
            }
        }

        function refreshAlerts() {
            loadAlerts();
        }

        // 分页控制
        function goToMonitorPage(page) {
            if (page >= 1 && page <= monitorTotalPages.value) {
                monitorCurrentPage.value = page;
            }
        }

        // 库存状态相关
        function getStockStatusClass(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            return status;
        }

        function getStockStatusText(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            const statusMap = {
                'normal': '正常',
                'low': '偏低',
                'critical': '紧急',
                'out': '缺货'
            };
            return statusMap[status] || '未知';
        }

        function getStockTextClass(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            return {
                'text-red-600': status === 'out' || status === 'critical',
                'text-orange-600': status === 'low',
                'text-green-600': status === 'normal'
            };
        }

        // 详情模态框
        async function viewItemDetails(item) {
            selectedItemDetails.value = item;
            showDetailsModal.value = true;
            
            // 加载该项目的事务记录
            try {
                const response = await getTransactions({
                    item_id: item.id,
                    item_type: item.type,
                    limit: 20,
                    sort: 'created_at',
                    order: 'desc'
                });
                
                if (response.success) {
                    itemTransactions.value = response.data || [];
                }
            } catch (error) {
                console.error('加载事务记录失败:', error);
                itemTransactions.value = [];
            }
        }

        function closeDetailsModal() {
            showDetailsModal.value = false;
            selectedItemDetails.value = null;
            itemTransactions.value = [];
        }

        return {
            // 状态
            activeTab,
            isLoadingTransactions,
            isLoadingMonitor,
            isLoadingAlerts,
            autoRefresh,
            // 数据
            stats,
            topMaterials,
            topProducts,
            recentTransactions,
            monitorItems,
            filteredMonitorItems,
            paginatedMonitorItems,
            monitorTotalPages,
            monitorCurrentPage,
            alerts,
            alertsCount,
            // 筛选
            monitorFilter,
            // 详情模态框
            showDetailsModal,
            selectedItemDetails,
            itemTransactions,
            // 方法
            refreshMonitorData,
            refreshAlerts,
            goToMonitorPage,
            getStockStatusClass,
            getStockStatusText,
            getStockTextClass,
            viewItemDetails,
            closeDetailsModal,
            formatDateTime,
            getOperationTypeText,
            // 权限检查
            hasPermission,
            canManage
        };
    },
    onUserLoaded: async (user) => {
        console.log('库存监控页面加载完成，当前用户:', user.username);
        // 设置全局用户信息，供权限检查使用
        window.currentUser = user;
    }
}).mount('#app');
