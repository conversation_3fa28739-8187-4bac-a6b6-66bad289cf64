/**
 * 物料管理控制器
 * 处理物料相关的HTTP请求
 */

const MaterialsService = require('../services/materialsService');
const logger = require('../../../utils/logger');

class MaterialsController {
    /**
     * 获取物料列表
     */
    async getMaterials(req, res) {
        try {
            const filters = {
                material_type: req.query.material_type,
                status: req.query.status,
                search: req.query.search,
                limit: req.query.limit
            };

            const materials = await MaterialsService.getMaterials(filters);
            
            res.json({
                success: true,
                data: materials,
                message: '获取物料列表成功'
            });
        } catch (error) {
            logger.error('获取物料列表失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取物料列表失败'
            });
        }
    }

    /**
     * 根据ID获取物料详情
     */
    async getMaterialById(req, res) {
        try {
            const { id } = req.params;
            const material = await MaterialsService.getMaterialById(id);
            
            res.json({
                success: true,
                data: material,
                message: '获取物料详情成功'
            });
        } catch (error) {
            logger.error('获取物料详情失败:', error);
            res.status(error.message === '物料不存在' ? 404 : 500).json({
                success: false,
                message: error.message || '获取物料详情失败'
            });
        }
    }

    /**
     * 创建物料
     */
    async createMaterial(req, res) {
        try {
            const materialData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['material_code', 'material_name', 'material_type', 'unit'];
            for (const field of requiredFields) {
                if (!materialData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            const result = await MaterialsService.createMaterial(materialData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '物料创建成功'
            });
        } catch (error) {
            logger.error('创建物料失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '创建物料失败'
            });
        }
    }

    /**
     * 更新物料
     */
    async updateMaterial(req, res) {
        try {
            const { id } = req.params;
            const materialData = req.body;
            const operatorId = req.user.id;

            const result = await MaterialsService.updateMaterial(id, materialData, operatorId);
            
            res.json({
                success: true,
                data: result,
                message: '物料更新成功'
            });
        } catch (error) {
            logger.error('更新物料失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 400).json({
                success: false,
                message: error.message || '更新物料失败'
            });
        }
    }

    /**
     * 删除物料
     */
    async deleteMaterial(req, res) {
        try {
            const { id } = req.params;
            const operatorId = req.user.id;

            const result = await MaterialsService.deleteMaterial(id, operatorId);
            
            res.json({
                success: true,
                data: result,
                message: '物料删除成功'
            });
        } catch (error) {
            logger.error('删除物料失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 400).json({
                success: false,
                message: error.message || '删除物料失败'
            });
        }
    }

    /**
     * 物料入库
     */
    async materialInbound(req, res) {
        try {
            const inboundData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['material_id', 'quantity'];
            for (const field of requiredFields) {
                if (!inboundData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (inboundData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '入库数量必须大于0'
                });
            }

            const result = await MaterialsService.materialInbound(inboundData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '物料入库成功'
            });
        } catch (error) {
            logger.error('物料入库失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '物料入库失败'
            });
        }
    }

    /**
     * 物料出库
     */
    async materialOutbound(req, res) {
        try {
            const outboundData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['material_id', 'quantity'];
            for (const field of requiredFields) {
                if (!outboundData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (outboundData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '出库数量必须大于0'
                });
            }

            const result = await MaterialsService.materialOutbound(outboundData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '物料出库成功'
            });
        } catch (error) {
            logger.error('物料出库失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '物料出库失败'
            });
        }
    }

    /**
     * 物料退料
     */
    async materialReturn(req, res) {
        try {
            const returnData = req.body;
            const operatorId = req.user.id;

            // 验证必填字段
            const requiredFields = ['material_id', 'quantity'];
            for (const field of requiredFields) {
                if (!returnData[field]) {
                    return res.status(400).json({
                        success: false,
                        message: `缺少必填字段: ${field}`
                    });
                }
            }

            // 验证数量为正数
            if (returnData.quantity <= 0) {
                return res.status(400).json({
                    success: false,
                    message: '退料数量必须大于0'
                });
            }

            const result = await MaterialsService.materialReturn(returnData, operatorId);
            
            res.status(201).json({
                success: true,
                data: result,
                message: '物料退料成功'
            });
        } catch (error) {
            logger.error('物料退料失败:', error);
            res.status(400).json({
                success: false,
                message: error.message || '物料退料失败'
            });
        }
    }

    /**
     * 获取物料库存报告
     */
    async getMaterialsInventory(req, res) {
        try {
            const filters = {
                stock_status: req.query.stock_status
            };

            const inventory = await MaterialsService.getMaterialsInventory(filters);
            
            res.json({
                success: true,
                data: inventory,
                message: '获取物料库存报告成功'
            });
        } catch (error) {
            logger.error('获取物料库存报告失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取物料库存报告失败'
            });
        }
    }
}

module.exports = new MaterialsController();
