/* 仓库管理模块通用样式文件 - Warehouse Common CSS */

/* 此文件包含仓库管理模块的通用样式 */
/* 基础样式继承自 /assets/css/common.css */

/* 仓库操作卡片样式 */
.warehouse-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.warehouse-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* 操作类型标签样式 */
.operation-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.operation-badge.inbound {
    background-color: #DCFCE7;
    color: #166534;
}

.operation-badge.outbound {
    background-color: #FEF3C7;
    color: #92400E;
}

.operation-badge.return {
    background-color: #DBEAFE;
    color: #1E40AF;
}

/* 库存状态指示器 */
.stock-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.stock-indicator.normal {
    background-color: #10B981;
}

.stock-indicator.low {
    background-color: #F59E0B;
}

.stock-indicator.critical {
    background-color: #EF4444;
}

.stock-indicator.out {
    background-color: #6B7280;
}

/* 二维码扫描区域样式 */
.qr-scan-area {
    border: 2px dashed #D1D5DB;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #F9FAFB;
    transition: all 0.2s ease;
}

.qr-scan-area.active {
    border-color: #3B82F6;
    background-color: #EFF6FF;
}

.qr-scan-area:hover {
    border-color: #9CA3AF;
    background-color: #F3F4F6;
}

/* 数量输入框样式 */
.quantity-input {
    width: 120px;
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #D1D5DB;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background-color: #F3F4F6;
    border-color: #9CA3AF;
}

.quantity-btn:active {
    background-color: #E5E7EB;
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 操作历史时间线样式 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #E5E7EB;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3B82F6;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #E5E7EB;
}

.timeline-content {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索和筛选区域样式 */
.filter-section {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #E5E7EB;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6B7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .warehouse-card {
        padding: 1rem;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .stats-card {
        text-align: center;
    }
    
    .quantity-input {
        width: 100px;
    }
}

/* 打印样式 */
@media print {
    .warehouse-card {
        box-shadow: none;
        border: 1px solid #E5E7EB;
    }
    
    .timeline::before {
        background: #000;
    }
    
    .timeline-item::before {
        background: #000;
        border-color: #fff;
        box-shadow: none;
    }
}

/* 加载动画 */
.warehouse-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.warehouse-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E5E7EB;
    border-top: 4px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表格样式增强 */
.warehouse-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.warehouse-table th {
    background: #F9FAFB;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #E5E7EB;
}

.warehouse-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #F3F4F6;
}

.warehouse-table tr:hover {
    background-color: #F9FAFB;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn.primary {
    background: #3B82F6;
    color: white;
}

.action-btn.primary:hover {
    background: #2563EB;
}

.action-btn.secondary {
    background: #6B7280;
    color: white;
}

.action-btn.secondary:hover {
    background: #4B5563;
}

.action-btn.success {
    background: #10B981;
    color: white;
}

.action-btn.success:hover {
    background: #059669;
}

.action-btn.warning {
    background: #F59E0B;
    color: white;
}

.action-btn.warning:hover {
    background: #D97706;
}

.action-btn.danger {
    background: #EF4444;
    color: white;
}

.action-btn.danger:hover {
    background: #DC2626;
}
