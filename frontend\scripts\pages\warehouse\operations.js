/**
 * 仓库管理 - 操作记录页面
 * 提供快速操作、操作记录查询和批量操作功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import { 
    getMaterials, getProducts, getTransactions,
    materialInbound, materialOutbound, materialReturn,
    productInbound, productOutbound, productReturn,
    validateQRCode, getStockStatus, formatDateTime, getOperationTypeText
} from '../../api/warehouse.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    requiredPermissions: ['warehouse_view'], // 需要仓库查看权限
    components: {
        Sidebar
    },
    setup() {
        const { ref, reactive, onMounted, watch, computed } = Vue;

        // 权限检查
        const hasPermission = (permission) => {
            const user = window.currentUser;
            return user && user.permissions && user.permissions.includes(permission);
        };

        // 权限计算属性
        const canManage = computed(() => hasPermission('warehouse_manage'));
        const canInbound = computed(() => hasPermission('warehouse_inbound'));
        const canOutbound = computed(() => hasPermission('warehouse_outbound'));
        const canReturn = computed(() => hasPermission('warehouse_return'));

        // 页面状态
        const activeTab = ref('quick'); // 'quick' | 'records' | 'batch'
        const isSubmitting = ref(false);
        const isScanning = ref(false);
        const isLoadingRecords = ref(false);

        // 快速操作状态
        const quickOperation = ref(''); // 'inbound' | 'outbound' | 'return'
        const selectedItem = ref(null);
        const qrCodeInput = ref('');
        const availableItems = ref([]);

        // 手动选择状态
        const manualSelection = reactive({
            type: '', // 'material' | 'finished_product'
            itemId: ''
        });

        // 操作表单
        const operationForm = reactive({
            quantity: 1,
            notes: '',
            department: '',
            reason: ''
        });

        // 记录查询状态
        const records = ref([]);
        const recordsFilter = reactive({
            operationType: '',
            itemType: '',
            startDate: '',
            endDate: '',
            search: ''
        });
        const recordsCurrentPage = ref(1);
        const recordsItemsPerPage = ref(20);

        // 计算属性
        const canSubmit = computed(() => {
            if (!selectedItem.value || !operationForm.quantity) return false;
            if (quickOperation.value === 'outbound' && operationForm.quantity > selectedItem.value.current_stock) return false;
            return true;
        });

        const filteredRecords = computed(() => {
            let filtered = [...records.value];

            // 操作类型筛选
            if (recordsFilter.operationType) {
                filtered = filtered.filter(record => record.operation_type === recordsFilter.operationType);
            }

            // 项目类型筛选
            if (recordsFilter.itemType) {
                filtered = filtered.filter(record => record.item_type === recordsFilter.itemType);
            }

            // 日期范围筛选
            if (recordsFilter.startDate) {
                filtered = filtered.filter(record => record.created_at >= recordsFilter.startDate);
            }
            if (recordsFilter.endDate) {
                filtered = filtered.filter(record => record.created_at <= recordsFilter.endDate + 'T23:59:59');
            }

            // 搜索筛选
            if (recordsFilter.search) {
                const term = recordsFilter.search.toLowerCase();
                filtered = filtered.filter(record =>
                    (record.item_name && record.item_name.toLowerCase().includes(term)) ||
                    (record.item_code && record.item_code.toLowerCase().includes(term)) ||
                    (record.transaction_id && record.transaction_id.toLowerCase().includes(term))
                );
            }

            return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        });

        const paginatedRecords = computed(() => {
            const start = (recordsCurrentPage.value - 1) * recordsItemsPerPage.value;
            const end = start + recordsItemsPerPage.value;
            return filteredRecords.value.slice(start, end);
        });

        const recordsTotalPages = computed(() => 
            Math.ceil(filteredRecords.value.length / recordsItemsPerPage.value)
        );

        // 监听器
        watch(activeTab, (newTab) => {
            if (newTab === 'records') {
                loadRecords();
            }
        });

        // 页面初始化
        onMounted(() => {
            // 设置默认日期范围（最近30天）
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            recordsFilter.endDate = today.toISOString().split('T')[0];
            recordsFilter.startDate = thirtyDaysAgo.toISOString().split('T')[0];
        });

        // 快速操作相关方法
        function selectQuickOperation(operation) {
            quickOperation.value = operation;
            clearQuickOperation();
        }

        function clearQuickOperation() {
            selectedItem.value = null;
            qrCodeInput.value = '';
            manualSelection.type = '';
            manualSelection.itemId = '';
            Object.assign(operationForm, {
                quantity: 1,
                notes: '',
                department: '',
                reason: ''
            });
        }

        // 二维码验证
        async function validateQRCode() {
            if (!qrCodeInput.value.trim()) {
                alert('请输入二维码');
                return;
            }

            try {
                isScanning.value = true;
                const response = await validateQRCode(qrCodeInput.value.trim());
                
                if (response.success) {
                    const qrInfo = response.data;
                    
                    // 根据二维码信息获取对应的物料或成品
                    let itemResponse;
                    if (qrInfo.item_type === 'material') {
                        itemResponse = await getMaterials();
                    } else {
                        itemResponse = await getProducts();
                    }
                    
                    if (itemResponse.success) {
                        const item = itemResponse.data.find(i => 
                            i.id === qrInfo.item_id || 
                            i.code === qrInfo.item_code
                        );
                        
                        if (item) {
                            selectedItem.value = { ...item, type: qrInfo.item_type };
                            operationForm.quantity = 1;
                            alert('二维码验证成功！');
                        } else {
                            alert('未找到对应的' + (qrInfo.item_type === 'material' ? '物料' : '成品'));
                        }
                    }
                } else {
                    alert('二维码验证失败: ' + response.message);
                }
            } catch (error) {
                console.error('二维码验证失败:', error);
                alert('二维码验证失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isScanning.value = false;
            }
        }

        // 加载可选项目
        async function loadItemsForSelection() {
            if (!manualSelection.type) {
                availableItems.value = [];
                return;
            }

            try {
                let response;
                if (manualSelection.type === 'material') {
                    response = await getMaterials();
                } else {
                    response = await getProducts();
                }

                if (response.success) {
                    availableItems.value = response.data || [];
                } else {
                    availableItems.value = [];
                }
            } catch (error) {
                console.error('加载项目失败:', error);
                availableItems.value = [];
            }
        }

        // 手动选择项目
        function selectManualItem() {
            if (!manualSelection.itemId) {
                selectedItem.value = null;
                return;
            }

            const item = availableItems.value.find(i => i.id === manualSelection.itemId);
            if (item) {
                selectedItem.value = { ...item, type: manualSelection.type };
                operationForm.quantity = 1;
            }
        }

        // 数量控制
        function increaseQuantity() {
            operationForm.quantity = (operationForm.quantity || 0) + 1;
        }

        function decreaseQuantity() {
            if (operationForm.quantity > 1) {
                operationForm.quantity = operationForm.quantity - 1;
            }
        }

        // 提交快速操作
        async function submitQuickOperation() {
            if (!canSubmit.value) {
                alert('请检查输入信息');
                return;
            }

            try {
                isSubmitting.value = true;
                
                const data = {
                    item_id: selectedItem.value.id,
                    quantity: operationForm.quantity,
                    notes: operationForm.notes
                };

                // 添加特定字段
                if (quickOperation.value === 'outbound') {
                    data.department = operationForm.department;
                } else if (quickOperation.value === 'return') {
                    data.reason = operationForm.reason;
                }

                let response;
                const itemType = selectedItem.value.type;
                
                if (quickOperation.value === 'inbound') {
                    response = itemType === 'material' ? 
                        await materialInbound(data) : 
                        await productInbound(data);
                } else if (quickOperation.value === 'outbound') {
                    response = itemType === 'material' ? 
                        await materialOutbound(data) : 
                        await productOutbound(data);
                } else if (quickOperation.value === 'return') {
                    response = itemType === 'material' ? 
                        await materialReturn(data) : 
                        await productReturn(data);
                }

                if (response.success) {
                    alert(getSubmitButtonText(quickOperation.value) + '成功！');
                    clearQuickOperation();
                    
                    // 如果当前在记录页面，刷新记录
                    if (activeTab.value === 'records') {
                        loadRecords();
                    }
                } else {
                    alert(getSubmitButtonText(quickOperation.value) + '失败: ' + response.message);
                }
            } catch (error) {
                console.error('操作失败:', error);
                alert('操作失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 加载操作记录
        async function loadRecords() {
            try {
                isLoadingRecords.value = true;
                
                const params = {
                    limit: 1000, // 获取更多记录用于前端筛选
                    sort: 'created_at',
                    order: 'desc'
                };

                const response = await getTransactions(params);
                
                if (response.success) {
                    records.value = response.data || [];
                } else {
                    records.value = [];
                    console.error('加载记录失败:', response.message);
                }
            } catch (error) {
                console.error('加载记录失败:', error);
                records.value = [];
            } finally {
                isLoadingRecords.value = false;
            }
        }

        // 搜索记录
        function searchRecords() {
            recordsCurrentPage.value = 1;
            // 筛选逻辑在计算属性中处理
        }

        // 清除记录筛选
        function clearRecordsFilter() {
            Object.assign(recordsFilter, {
                operationType: '',
                itemType: '',
                startDate: '',
                endDate: '',
                search: ''
            });
            
            // 重新设置默认日期范围
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            recordsFilter.endDate = today.toISOString().split('T')[0];
            recordsFilter.startDate = thirtyDaysAgo.toISOString().split('T')[0];
            
            recordsCurrentPage.value = 1;
        }

        // 导出记录
        function exportRecords() {
            if (filteredRecords.value.length === 0) {
                alert('没有可导出的记录');
                return;
            }

            // 创建CSV内容
            const headers = ['事务ID', '操作类型', '项目类型', '项目编号', '项目名称', '数量', '单位', '操作人', '部门/原因', '操作时间', '备注'];
            const csvContent = [
                headers.join(','),
                ...filteredRecords.value.map(record => [
                    record.transaction_id,
                    getOperationTypeText(record.operation_type),
                    record.item_type === 'material' ? '物料' : '成品',
                    record.item_code,
                    record.item_name,
                    record.quantity,
                    record.item_unit,
                    record.operator_name,
                    record.department || record.reason || '',
                    formatDateTime(record.created_at),
                    record.notes || ''
                ].map(field => `"${field}"`).join(','))
            ].join('\n');

            // 下载文件
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `warehouse_operations_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 分页控制
        function goToRecordsPage(page) {
            if (page >= 1 && page <= recordsTotalPages.value) {
                recordsCurrentPage.value = page;
            }
        }

        // 辅助方法
        function getOperationTitle(operation) {
            const titles = {
                'inbound': '入库操作',
                'outbound': '出库操作',
                'return': '退料返仓'
            };
            return titles[operation] || '';
        }

        function getQuantityLabel(operation) {
            const labels = {
                'inbound': '入库数量',
                'outbound': '出库数量',
                'return': '退料数量'
            };
            return labels[operation] || '数量';
        }

        function getSubmitButtonText(operation) {
            const texts = {
                'inbound': '确认入库',
                'outbound': '确认出库',
                'return': '确认退料'
            };
            return texts[operation] || '确认操作';
        }

        function getStockTextClass(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            return {
                'text-red-600': status === 'out' || status === 'critical',
                'text-orange-600': status === 'low',
                'text-green-600': status === 'normal'
            };
        }

        return {
            // 状态
            activeTab,
            isSubmitting,
            isScanning,
            isLoadingRecords,
            // 快速操作
            quickOperation,
            selectedItem,
            qrCodeInput,
            availableItems,
            manualSelection,
            operationForm,
            canSubmit,
            // 记录查询
            records,
            filteredRecords,
            paginatedRecords,
            recordsTotalPages,
            recordsCurrentPage,
            recordsFilter,
            // 方法
            selectQuickOperation,
            clearQuickOperation,
            validateQRCode,
            loadItemsForSelection,
            selectManualItem,
            increaseQuantity,
            decreaseQuantity,
            submitQuickOperation,
            loadRecords,
            searchRecords,
            clearRecordsFilter,
            exportRecords,
            goToRecordsPage,
            getOperationTitle,
            getQuantityLabel,
            getSubmitButtonText,
            getStockTextClass,
            formatDateTime,
            getOperationTypeText,
            // 权限检查
            hasPermission,
            canManage,
            canInbound,
            canOutbound,
            canReturn
        };
    },
    onUserLoaded: async (user) => {
        console.log('操作记录页面加载完成，当前用户:', user.username);
        // 设置全局用户信息，供权限检查使用
        window.currentUser = user;
    }
}).mount('#app');
