<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表追溯 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/css/libs/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/warehouse/common.css">
    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 移动端菜单按钮 -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden fixed top-4 left-4 z-20 bg-white p-2 rounded-md shadow-md border border-gray-200">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- 移动端遮罩层 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-15"></div>

        <!-- 侧边导航栏 -->
        <sidebar :user="currentUser" :sidebar-open="sidebarOpen"></sidebar>

        <div class="flex-1 ml-0 md:ml-72 p-2 md:p-4">
            <!-- 面包屑导航 -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="inline-flex items-center text-sm font-medium text-gray-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            仓库管理 - 报表追溯
                        </span>
                    </li>
                </ol>
            </nav>

            <div class="bg-white rounded-lg shadow-md p-3 md:p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 md:mb-6">报表追溯</h2>

                <!-- 功能选项卡 -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8">
                        <button
                            @click="activeTab = 'reports'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200"
                            :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'reports'}">
                            <i class="fas fa-chart-bar mr-1"></i>
                            报表生成
                        </button>
                        <button
                            @click="activeTab = 'traceability'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-green-600 transition-colors duration-200"
                            :class="{'border-b-2 border-green-500 text-green-600': activeTab === 'traceability'}">
                            <i class="fas fa-search mr-1"></i>
                            追溯查询
                        </button>
                        <button
                            @click="activeTab = 'analytics'"
                            class="py-2 px-1 font-medium text-gray-600 hover:text-purple-600 transition-colors duration-200"
                            :class="{'border-b-2 border-purple-500 text-purple-600': activeTab === 'analytics'}">
                            <i class="fas fa-chart-line mr-1"></i>
                            数据分析
                        </button>
                    </div>
                </div>

                <!-- 报表生成 -->
                <div v-if="activeTab === 'reports'" class="space-y-6">
                    <!-- 报表类型选择 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectReportType('inventory')"
                             :class="{'ring-2 ring-blue-500': selectedReportType === 'inventory'}">
                            <div class="text-center">
                                <i class="fas fa-boxes text-4xl text-blue-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-blue-600">库存报表</h3>
                                <p class="text-sm text-gray-600 mt-2">当前库存状态和分布</p>
                            </div>
                        </div>

                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectReportType('transactions')"
                             :class="{'ring-2 ring-green-500': selectedReportType === 'transactions'}">
                            <div class="text-center">
                                <i class="fas fa-exchange-alt text-4xl text-green-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-green-600">事务报表</h3>
                                <p class="text-sm text-gray-600 mt-2">入库出库操作记录</p>
                            </div>
                        </div>

                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectReportType('summary')"
                             :class="{'ring-2 ring-purple-500': selectedReportType === 'summary'}">
                            <div class="text-center">
                                <i class="fas fa-chart-pie text-4xl text-purple-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-purple-600">汇总报表</h3>
                                <p class="text-sm text-gray-600 mt-2">统计分析和趋势</p>
                            </div>
                        </div>
                    </div>

                    <!-- 报表参数配置 -->
                    <div v-if="selectedReportType" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-cog text-gray-500 mr-2"></i>
                            报表参数配置
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">报表名称</label>
                                    <input type="text" v-model="reportConfig.name" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                           :placeholder="getDefaultReportName()">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日期范围</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="date" v-model="reportConfig.startDate" 
                                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <input type="date" v-model="reportConfig.endDate" 
                                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>

                                <div v-if="selectedReportType !== 'inventory'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                                    <select v-model="reportConfig.itemType" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">全部类型</option>
                                        <option value="material">物料</option>
                                        <option value="finished_product">成品</option>
                                    </select>
                                </div>

                                <div v-if="selectedReportType === 'transactions'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
                                    <select v-model="reportConfig.operationType" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">全部操作</option>
                                        <option value="inbound">入库</option>
                                        <option value="outbound">出库</option>
                                        <option value="return">退料/返仓</option>
                                    </select>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">报表格式</label>
                                    <select v-model="reportConfig.format" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="excel">Excel (.xlsx)</option>
                                        <option value="csv">CSV (.csv)</option>
                                        <option value="pdf">PDF (.pdf)</option>
                                    </select>
                                </div>

                                <div v-if="selectedReportType === 'inventory'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">库存状态筛选</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeNormal" class="mr-2">
                                            <span class="text-sm">正常库存</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeLow" class="mr-2">
                                            <span class="text-sm">低库存</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeCritical" class="mr-2">
                                            <span class="text-sm">紧急库存</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeOut" class="mr-2">
                                            <span class="text-sm">缺货</span>
                                        </label>
                                    </div>
                                </div>

                                <div v-if="selectedReportType === 'summary'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">统计维度</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeDaily" class="mr-2">
                                            <span class="text-sm">按日统计</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeWeekly" class="mr-2">
                                            <span class="text-sm">按周统计</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="checkbox" v-model="reportConfig.includeMonthly" class="mr-2">
                                            <span class="text-sm">按月统计</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <button @click="generateReport" :disabled="isGeneratingReport" class="action-btn primary">
                                        <i class="fas fa-file-export"></i>
                                        {{ isGeneratingReport ? '生成中...' : '生成报表' }}
                                    </button>
                                    <button @click="previewReport" :disabled="isGeneratingReport" class="action-btn secondary">
                                        <i class="fas fa-eye"></i>
                                        预览
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报表预览 -->
                    <div v-if="reportPreview" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-eye text-blue-500 mr-2"></i>
                            报表预览
                        </h3>
                        
                        <div class="overflow-x-auto">
                            <table class="warehouse-table">
                                <thead>
                                    <tr>
                                        <th v-for="header in reportPreview.headers" :key="header">{{ header }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(row, index) in reportPreview.data.slice(0, 10)" :key="index">
                                        <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div v-if="reportPreview.data.length > 10" class="text-sm text-gray-500 mt-2">
                            显示前10条记录，共{{ reportPreview.data.length }}条记录
                        </div>
                    </div>
                </div>

                <!-- 追溯查询 -->
                <div v-if="activeTab === 'traceability'" class="space-y-6">
                    <!-- 查询方式选择 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectTraceMethod('qrcode')"
                             :class="{'ring-2 ring-blue-500': traceMethod === 'qrcode'}">
                            <div class="text-center">
                                <i class="fas fa-qrcode text-4xl text-blue-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-blue-600">二维码追溯</h3>
                                <p class="text-sm text-gray-600 mt-2">扫描二维码查询完整追溯链</p>
                            </div>
                        </div>

                        <div class="warehouse-card cursor-pointer hover:shadow-lg transition-shadow" 
                             @click="selectTraceMethod('manual')"
                             :class="{'ring-2 ring-green-500': traceMethod === 'manual'}">
                            <div class="text-center">
                                <i class="fas fa-search text-4xl text-green-500 mb-3"></i>
                                <h3 class="text-lg font-semibold text-green-600">手动查询</h3>
                                <p class="text-sm text-gray-600 mt-2">通过编号或名称查询追溯信息</p>
                            </div>
                        </div>
                    </div>

                    <!-- 二维码追溯 -->
                    <div v-if="traceMethod === 'qrcode'" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-qrcode text-blue-500 mr-2"></i>
                            二维码追溯查询
                        </h3>
                        
                        <div class="qr-scan-area mb-6" :class="{'active': isScanning}">
                            <div class="text-center">
                                <i class="fas fa-qrcode text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">扫描二维码进行追溯查询</p>
                                <input type="text" v-model="traceQRCode" @keyup.enter="traceByQRCode" 
                                       placeholder="请扫描或输入二维码"
                                       class="w-full max-w-md mx-auto px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <button @click="traceByQRCode" class="mt-2 action-btn primary">
                                    <i class="fas fa-search"></i>
                                    开始追溯
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 手动查询 -->
                    <div v-if="traceMethod === 'manual'" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-search text-green-500 mr-2"></i>
                            手动追溯查询
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">查询类型</label>
                                <select v-model="manualTrace.type" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">请选择类型</option>
                                    <option value="material">物料</option>
                                    <option value="finished_product">成品</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">查询方式</label>
                                <select v-model="manualTrace.method" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="code">按编号查询</option>
                                    <option value="name">按名称查询</option>
                                    <option value="transaction">按事务ID查询</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">查询内容</label>
                                <div class="flex">
                                    <input type="text" v-model="manualTrace.query" @keyup.enter="traceManually"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                           :placeholder="getTracePlaceholder()">
                                    <button @click="traceManually" class="action-btn primary" style="border-radius: 0 0.375rem 0.375rem 0;">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 追溯结果 -->
                    <div v-if="traceResults" class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-route text-purple-500 mr-2"></i>
                            追溯结果
                        </h3>
                        
                        <div v-if="isTracing" class="warehouse-loading">
                            <div class="warehouse-spinner"></div>
                            <span class="ml-2 text-gray-600">追溯查询中...</span>
                        </div>
                        
                        <div v-else-if="traceResults.length === 0" class="text-center text-gray-500 py-8">
                            <i class="fas fa-search text-4xl text-gray-300 mb-2"></i>
                            <p>未找到相关追溯信息</p>
                        </div>
                        
                        <div v-else class="timeline">
                            <div v-for="(result, index) in traceResults" :key="index" class="timeline-item">
                                <div class="timeline-content">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center space-x-2">
                                                <span class="operation-badge" :class="result.operation_type">
                                                    {{ getOperationTypeText(result.operation_type) }}
                                                </span>
                                                <span class="text-sm font-medium">{{ result.item_name }}</span>
                                                <span class="text-xs text-gray-500">({{ result.item_code }})</span>
                                            </div>
                                            <div class="text-sm text-gray-600 mt-1">
                                                <div>数量: {{ result.quantity }} {{ result.item_unit }}</div>
                                                <div>操作人: {{ result.operator_name }}</div>
                                                <div v-if="result.department">部门: {{ result.department }}</div>
                                                <div v-if="result.reason">原因: {{ result.reason }}</div>
                                                <div v-if="result.notes">备注: {{ result.notes }}</div>
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            {{ formatDateTime(result.created_at) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据分析 -->
                <div v-if="activeTab === 'analytics'" class="space-y-6">
                    <div class="warehouse-card">
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-chart-line text-purple-500 mr-2"></i>
                            数据分析
                        </h3>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-chart-line text-yellow-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">数据分析功能</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>数据分析功能正在开发中，敬请期待。该功能将提供：</p>
                                        <ul class="list-disc list-inside mt-2 space-y-1">
                                            <li>库存趋势分析和预测</li>
                                            <li>出入库频率统计</li>
                                            <li>库存周转率分析</li>
                                            <li>异常操作检测</li>
                                            <li>成本效益分析</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center py-12">
                            <i class="fas fa-chart-line text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-500 mb-2">功能开发中</h3>
                            <p class="text-gray-400">数据分析功能即将上线，请使用报表生成功能获取基础数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/global.js"></script>
    <script type="module" src="/scripts/pages/warehouse/reports.js"></script>
</body>
</html>
