/**
 * 生产排程数据访问层
 * 处理排程相关的数据库操作
 */

const databaseManager = require('./database');
const { ScheduleModel } = require('../models/scheduleModel');
const logger = require('../utils/logger');

class ScheduleRepository {
    constructor() {
        this.db = databaseManager.getConnection();
        // 只在详细日志模式下显示初始化信息
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('排程数据访问层初始化完成');
        }
    }

    /**
     * 创建排程
     * @param {ScheduleModel} schedule 排程模型
     * @returns {Promise<ScheduleModel>} 创建的排程
     */
    async create(schedule) {
        try {
            const dbData = schedule.toDatabase();

            const stmt = this.db.prepare(`
                INSERT INTO schedules (
                    id, title, product_id, product_name, quantity,
                    start_time, end_time, status, priority,
                    assigned_equipment, assigned_personnel, required_materials,
                    progress, notes, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            stmt.run(
                dbData.id, dbData.title, dbData.product_id, dbData.product_name,
                dbData.quantity, dbData.start_time, dbData.end_time,
                dbData.status, dbData.priority, dbData.assigned_equipment,
                dbData.assigned_personnel, dbData.required_materials,
                dbData.progress, dbData.notes, dbData.created_by,
                dbData.created_at, dbData.updated_at
            );

            logger.info('排程创建成功', { scheduleId: schedule.id });
            return schedule;
        } catch (error) {
            logger.error('排程创建失败', { error: error.message, scheduleId: schedule.id });
            throw error;
        }
    }

    /**
     * 根据ID获取排程
     * @param {string} id 排程ID
     * @returns {Promise<ScheduleModel|null>} 排程模型
     */
    async findById(id) {
        try {
            const stmt = this.db.prepare('SELECT * FROM schedules WHERE id = ?');
            const row = stmt.get(id);

            if (!row) {
                return null;
            }

            return ScheduleModel.fromDatabase(row);
        } catch (error) {
            logger.error('获取排程失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 获取排程列表
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 排程列表和总数
     */
    async findAll(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                status,
                startDate,
                endDate,
                priority
            } = options;

            let whereClause = '';
            const params = [];

            // 构建查询条件
            const conditions = [];

            if (status) {
                conditions.push('status = ?');
                params.push(status);
            }

            if (startDate) {
                conditions.push('start_time >= ?');
                params.push(startDate);
            }

            if (endDate) {
                conditions.push('end_time <= ?');
                params.push(endDate);
            }

            if (priority) {
                conditions.push('priority = ?');
                params.push(priority);
            }

            if (conditions.length > 0) {
                whereClause = 'WHERE ' + conditions.join(' AND ');
            }

            // 获取总数
            const countStmt = this.db.prepare(`SELECT COUNT(*) as total FROM schedules ${whereClause}`);
            const { total } = countStmt.get(...params);

            // 获取分页数据
            const offset = (page - 1) * limit;
            const dataStmt = this.db.prepare(`
                SELECT * FROM schedules ${whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `);

            const rows = dataStmt.all(...params, limit, offset);
            const schedules = rows.map(row => ScheduleModel.fromDatabase(row));

            return {
                schedules,
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            logger.error('获取排程列表失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 更新排程
     * @param {string} id 排程ID
     * @param {Object} updateData 更新数据
     * @returns {Promise<ScheduleModel|null>} 更新后的排程
     */
    async update(id, updateData) {
        try {
            const existingSchedule = await this.findById(id);
            if (!existingSchedule) {
                return null;
            }

            // 合并更新数据
            const updatedSchedule = new ScheduleModel({
                ...existingSchedule,
                ...updateData,
                id: id, // 确保ID不被覆盖
                updatedAt: new Date().toISOString()
            });

            const dbData = updatedSchedule.toDatabase();

            const stmt = this.db.prepare(`
                UPDATE schedules SET
                    title = ?, product_id = ?, product_name = ?, quantity = ?,
                    start_time = ?, end_time = ?, status = ?, priority = ?,
                    assigned_equipment = ?, assigned_personnel = ?, required_materials = ?,
                    progress = ?, notes = ?, updated_at = ?
                WHERE id = ?
            `);

            stmt.run(
                dbData.title, dbData.product_id, dbData.product_name, dbData.quantity,
                dbData.start_time, dbData.end_time, dbData.status, dbData.priority,
                dbData.assigned_equipment, dbData.assigned_personnel, dbData.required_materials,
                dbData.progress, dbData.notes, dbData.updated_at, id
            );

            logger.info('排程更新成功', { scheduleId: id });
            return updatedSchedule;
        } catch (error) {
            logger.error('排程更新失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 删除排程
     * @param {string} id 排程ID
     * @returns {Promise<boolean>} 删除结果
     */
    async delete(id) {
        try {
            const stmt = this.db.prepare('DELETE FROM schedules WHERE id = ?');
            const result = stmt.run(id);

            const success = result.changes > 0;
            if (success) {
                logger.info('排程删除成功', { scheduleId: id });
            } else {
                logger.warn('排程删除失败，排程不存在', { scheduleId: id });
            }

            return success;
        } catch (error) {
            logger.error('排程删除失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }

    /**
     * 更新排程状态
     * @param {string} id 排程ID
     * @param {string} status 新状态
     * @returns {Promise<boolean>} 更新结果
     */
    async updateStatus(id, status) {
        try {
            const stmt = this.db.prepare(`
                UPDATE schedules SET status = ?, updated_at = ?
                WHERE id = ?
            `);

            const result = stmt.run(status, new Date().toISOString(), id);
            const success = result.changes > 0;

            if (success) {
                logger.info('排程状态更新成功', { scheduleId: id, status });
            }

            return success;
        } catch (error) {
            logger.error('排程状态更新失败', { error: error.message, scheduleId: id });
            throw error;
        }
    }
}

module.exports = ScheduleRepository;
