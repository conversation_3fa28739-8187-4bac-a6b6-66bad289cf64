const os = require('os');

/**
 * 获取所有可用的网络访问地址
 * @param {number} port - 服务器端口
 * @returns {Object} 包含各种访问地址的对象
 */
function getNetworkAddresses(port) {
    const addresses = {
        local: `http://localhost:${port}`,
        network: []
    };

    try {
        const networkInterfaces = os.networkInterfaces();
        
        // 遍历所有网络接口
        Object.keys(networkInterfaces).forEach(interfaceName => {
            const interfaces = networkInterfaces[interfaceName];
            
            interfaces.forEach(iface => {
                // 只处理IPv4地址，排除内部回环地址
                if (iface.family === 'IPv4' && !iface.internal) {
                    addresses.network.push({
                        name: interfaceName,
                        address: `http://${iface.address}:${port}`,
                        ip: iface.address,
                        type: getAddressType(iface.address)
                    });
                }
            });
        });

        // 按地址类型排序：局域网地址优先
        addresses.network.sort((a, b) => {
            const priority = { 'private': 1, 'public': 2 };
            return (priority[a.type] || 3) - (priority[b.type] || 3);
        });

    } catch (error) {
        console.warn('获取网络接口信息失败:', error.message);
    }

    return addresses;
}

/**
 * 判断IP地址类型
 * @param {string} ip - IP地址
 * @returns {string} 地址类型：'private' 或 'public'
 */
function getAddressType(ip) {
    const parts = ip.split('.').map(Number);
    
    // 私有地址范围
    if (
        (parts[0] === 192 && parts[1] === 168) ||  // 192.168.x.x
        (parts[0] === 10) ||                       // 10.x.x.x
        (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) // 172.16-31.x.x
    ) {
        return 'private';
    }
    
    return 'public';
}

/**
 * 检查IP是否在白名单中
 * @param {string} ip - 要检查的IP地址
 * @param {Array} whitelist - IP白名单数组
 * @returns {boolean} 是否在白名单中
 */
function isIPInWhitelist(ip, whitelist) {
    if (!whitelist || whitelist.length === 0) {
        return true; // 没有白名单限制，允许所有IP
    }

    // 处理localhost特殊情况
    if (ip === 'localhost' || ip === '127.0.0.1') {
        return whitelist.some(range =>
            range === 'localhost' ||
            range === '127.0.0.1' ||
            isIPInRange('127.0.0.1', range)
        );
    }

    return whitelist.some(range => isIPInRange(ip, range));
}

/**
 * IP地址匹配工具函数（简化版，用于白名单检查）
 */
function isIPInRange(ip, range) {
    // 处理localhost特殊情况
    if (range === 'localhost') {
        return ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
    }

    // 处理单个IP
    if (!range.includes('/') && !range.includes('-')) {
        return ip === range;
    }

    // 处理CIDR格式 (如: ***********/24)
    if (range.includes('/')) {
        const [network, prefixLength] = range.split('/');
        const prefix = parseInt(prefixLength);

        // 将IP地址转换为32位整数
        const ipToInt = (ipStr) => {
            return ipStr.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
        };

        const ipInt = ipToInt(ip);
        const networkInt = ipToInt(network);
        const mask = (0xFFFFFFFF << (32 - prefix)) >>> 0;

        return (ipInt & mask) === (networkInt & mask);
    }

    // 处理IP范围格式 (如: ***********-*************)
    if (range.includes('-')) {
        const [startIP, endIP] = range.split('-');
        const ipToInt = (ipStr) => {
            return ipStr.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
        };

        const ipInt = ipToInt(ip);
        const startInt = ipToInt(startIP.trim());
        const endInt = ipToInt(endIP.trim());

        return ipInt >= startInt && ipInt <= endInt;
    }

    return false;
}

/**
 * 格式化网络地址显示信息（考虑IP白名单）
 * @param {number} port - 服务器端口
 * @param {Array} ipWhitelist - IP白名单配置
 * @returns {string} 格式化的地址信息
 */
function formatNetworkAddresses(port, ipWhitelist = []) {
    const addresses = getNetworkAddresses(port);
    let output = [];

    // 如果没有IP白名单限制，显示所有地址
    if (ipWhitelist.length === 0) {
        output.push(`📍 本地访问: ${addresses.local}`);

        if (addresses.network.length > 0) {
            addresses.network.forEach(addr => {
                const typeLabel = addr.type === 'private' ? '局域网访问' : '网络访问';
                output.push(`📍 ${typeLabel}: ${addr.address}`);
            });
        }

        return output.join('\n');
    }

    // 有IP白名单限制，只显示允许的地址
    // 检查本地访问是否被允许
    if (isIPInWhitelist('localhost', ipWhitelist)) {
        output.push(`📍 本地访问: ${addresses.local}`);
    }

    // 过滤网络访问地址，只显示白名单允许的
    if (addresses.network.length > 0) {
        const allowedAddresses = addresses.network.filter(addr =>
            isIPInWhitelist(addr.ip, ipWhitelist)
        );

        allowedAddresses.forEach(addr => {
            const typeLabel = addr.type === 'private' ? '局域网访问' : '网络访问';
            output.push(`📍 ${typeLabel}: ${addr.address}`);
        });
    }

    // 如果启用了IP白名单但没有允许的地址，显示提示
    if (output.length === 0) {
        output.push(`⚠️  IP白名单已启用，当前网络接口均不在允许范围内`);
        output.push(`🔧 白名单配置: ${ipWhitelist.join(', ')}`);
    } else {
        // 显示安全提示
        output.push(`🛡️  IP访问控制已启用，仅白名单内IP可访问`);
    }

    return output.join('\n');
}

module.exports = {
    getNetworkAddresses,
    getAddressType,
    formatNetworkAddresses
};
