# 企业管理系统功能说明手册

## 最新更新 (2025-07-30)

### 🔧 新增功能
- **系统日志管理**：企业级现代化日志管理系统，提供专业的日志查看和管理功能
  - 现代化UI设计：采用现代化卡片式设计，渐变背景，玻璃态效果
  - 高级日志查看器：支持虚拟滚动，实时刷新，自动刷新模式
  - 智能搜索系统：实时搜索，搜索历史记录，快速搜索预设
  - 多维度过滤：日志级别过滤（Error/Warn/Info/Debug/HTTP），时间范围过滤
  - 文件管理功能：日志文件列表，删除确认，批量清理过期日志
  - 专业级功能：日志详情查看，一键复制导出，快捷键支持
  - 权限安全控制：仅限管理员访问，确保系统安全性

- **仓库管理系统**：完整的仓库管理解决方案，支持物料成品统一管理
  - 物料成品管理：统一管理界面，支持物料和成品的增删改查操作
  - 二维码标识系统：自动生成和管理二维码，支持扫码快速操作
  - 库存实时监控：实时库存数据展示，智能预警和统计分析
  - 出入库操作：支持入库、出库、退库等完整业务流程
  - 操作记录查询：完整的操作历史记录和高级筛选功能
  - 报表生成分析：库存报表、交易报表、汇总报表和数据追溯
  - 权限控制管理：基于角色的权限控制，确保操作安全性

- **电子签名集成到申请书模板**：在申请书模板中自动显示各级审批人的电子签名
  - 签名动态显示：根据审批历史动态显示厂长、总监、经理、CEO的电子签名
  - PDF签名导出：生成的PDF文件包含完整的电子签名信息
  - 统一认证存储：使用sessionStorage统一存储认证信息，确保签名正常显示

### 💡 技术实现
- **系统日志管理架构**：
  - 后端服务层：LogViewer服务，支持日志文件读取、搜索、过滤、清理
  - 前端组件：LogFileList、LogViewer、LogSearch、LogFilters四大核心组件
  - API接口：/api/logs/files、/api/logs/content、/api/logs/search、/api/logs/cleanup
  - 现代化UI：Vue 3 Composition API + Tailwind CSS + 自定义动画系统
  - 权限控制：仅限admin角色访问，确保系统安全性
  - 性能优化：虚拟滚动、懒加载、防抖搜索、内存管理

- **仓库管理模块化架构**：
  - 后端服务层：MaterialsService、ProductsService、TransactionsService、QRCodeService
  - 数据库设计：warehouse_materials、warehouse_finished_products、warehouse_inventory_transactions、warehouse_qrcodes
  - 前端组件：4个统一页面（物料成品管理、库存监控、操作记录、报表追溯）
  - API接口：完整的RESTful API设计，支持所有仓库管理操作
  - 权限集成：warehouse_view、warehouse_inbound、warehouse_outbound、warehouse_return、warehouse_manage

- **SignatureDisplay组件**：专用的签名显示组件，支持多种显示模式和尺寸
- **ApplicationTemplate组件**：申请书模板组件，集成签名显示和PDF生成
- **动态签名插入**：PDF生成时自动将签名插入到对应的审批区域
- **高质量渲染**：优化html2canvas配置，确保签名在PDF中清晰显示
- **多签名布局**：自动处理多个厂长/经理的签名排版
- **signatureConverter工具**：签名转换和优化工具，支持Base64转换和图片压缩

### 📋 功能配置
- **系统日志管理配置**：
  - 页面路由：/pages/system-management/logs.html
  - 权限配置：仅限admin角色访问，通过createStandardApp手动权限检查
  - 日志文件路径：backend/logs/目录下的所有.log文件
  - 支持的日志级别：Error、Warn、Info、Debug、HTTP
  - 清理配置：支持按天数清理过期日志文件（默认30天）
  - 搜索配置：实时搜索、搜索历史、快速搜索预设
  - 导出功能：支持日志内容导出为文本文件

- **仓库管理配置**：
  - 页面路由：/warehouse/materials-products、/warehouse/inventory-monitor、/warehouse/operations、/warehouse/reports
  - 权限配置：基于角色的访问控制，支持查看、入库、出库、退库、管理等权限
  - 二维码配置：自动生成唯一二维码，支持批量生成和验证
  - 库存预警：可配置库存阈值，自动生成预警信息
  - 数据导出：支持CSV、Excel等格式的数据导出功能

- **签名尺寸**：支持small、normal、large三种尺寸配置
- **显示模式**：支持单个签名和多个签名的不同布局模式
- **无签名处理**：优雅显示无电子签名的审批人信息
- **移动端适配**：响应式设计，支持移动设备查看和操作
- **双重存储机制**：文件路径存储 + Base64数据存储，确保签名可靠显示

### ✅ 最新修复 (2025-07-29)
- **仓库管理系统完整实现**：完成了完整的仓库管理系统开发和集成
  - 修复数据库连接模式：统一使用 `databaseManager.getConnection()` 模式
  - 修复中间件路径：更正 `../../middlewares/auth` 导入路径
  - 修复静态资源路由：解决JavaScript模块MIME类型错误
  - 修复API导入问题：重写warehouse.js API文件，使用正确的axios模式
  - 修复侧边栏路径冲突：完全解决仓库管理和生产排程中产品管理的路径冲突
- **电子签名显示问题**：修复了申请书模板中电子签名无法显示的403错误
- **认证存储统一**：统一使用sessionStorage存储认证信息，解决localStorage/sessionStorage不一致问题
- **路由优化**：优化了用户路由顺序，确保具体路由（如/:id/signature）优先于通用路由（如/:id）
- **权限控制完善**：所有认证用户都可以查看电子签名，无需额外权限限制

### ⚠️ 注意事项
- **仓库管理注意事项**：
  - 所有仓库操作都需要相应权限，确保操作安全性
  - 二维码生成后不可修改，请确保物料/成品信息准确
  - 库存数据实时更新，建议定期备份重要数据
  - 出入库操作会自动记录操作人和时间，确保追溯完整性
  - 系统支持并发操作，但建议避免同时对同一物料进行多次操作

- 电子签名需要在用户管理中预先上传，支持常见图片格式（JPG、PNG等）
- PDF生成时需要等待所有签名图片加载完成，确保显示完整
- 系统使用sessionStorage统一存储认证信息，浏览器关闭时自动清除，更安全
- 签名访问无需特殊权限，所有认证用户都可查看，确保申请书完整性

## 历史更新 (2025-07-22)

### 🔧 历史功能
- **设备健康度评估系统**：基于四维度评估体系的智能健康度评估功能
  - 四维度评估：设备年龄(20%)、维修频率(30%)、故障严重程度(30%)、保养情况(20%)
  - 健康度等级：优秀(90-100)、良好(80-89)、一般(70-79)、较差(60-69)、危险(0-59)
  - 故障预测：基于历史维修记录的智能故障预测算法
  - 维护建议：自动生成个性化维护建议和优先级排序
  - 统计分析：设备健康度分布统计和预警设备监控
  - 批量计算：支持批量计算所有设备的健康度评估

### 💡 技术实现
- **后端算法**：
  - `HealthCalculator`：四维度健康度计算引擎
  - `FaultPredictor`：故障预测算法，支持置信度评估
  - `RecommendationEngine`：智能维护建议生成器
  - `HealthAssessmentService`：健康度评估服务层
- **数据模型**：
  - 扩展 `equipment_health` 表支持四维度评分
  - 新增 `equipment_health_history` 表记录历史评估数据
- **前端界面**：
  - 健康度统计仪表板
  - 设备健康度详情模态框
  - 实时数据更新和交互式操作

## 📋 系统概述

本系统是一个基于Web的企业管理系统，采用前后端分离架构，支持多用户、多角色的申请流程管理。系统提供完整的申请生命周期管理，包括申请提交、审批流程、状态跟踪、文档管理等功能。

### 🏗️ 技术架构
- **前端**: Vue.js 3 (Composition API) + HTML5 + CSS3 + JavaScript ES6+
- **后端**: Node.js + Express.js
- **数据库**: SQLite + Repository模式
- **文件存储**: 本地文件系统 + 电子签名管理
- **认证**: JWT Token + sessionStorage统一存储
- **部署**: 支持本地部署和局域网访问

## 🎯 核心功能模块

### 1. 用户认证与权限管理

#### 1.1 用户登录系统
- **登录页面**: `/login`
- **功能特性**:
  - 用户名/密码认证
  - JWT Token生成和验证
  - 自动跳转到用户默认页面
  - 现代化UI设计，支持响应式布局
  - 底部装饰组件增强视觉效果

#### 1.2 用户管理
- **管理页面**: `/user/management`
- **权限要求**: 管理员权限
- **功能特性**:
  - 用户列表查看和管理
  - 用户信息编辑（用户名、部门、邮箱等）
  - 用户权限分配和管理
  - 用户状态控制（激活/禁用）
  - 电子签名管理（上传、查看、删除）
  - 批量权限操作

#### 1.3 权限模板管理
- **功能位置**: 用户管理页面的"权限模板"标签
- **功能特性**:
  - 预置权限模板管理
  - 自定义权限模板创建
  - 权限模板应用到用户
  - 权限模板编辑和删除

#### 1.4 用户设置
- **设置页面**: `/user/settings`
- **功能特性**:
  - 个人信息修改
  - 密码修改
  - 个人偏好设置

### 2. 申请管理系统

#### 2.1 申请提交
- **新建申请页面**: `/application/new`
- **权限要求**: `new_application`
- **功能特性**:
  - 多种申请类型支持（标准申请、其他申请）
  - 申请信息填写（申请人、部门、内容、金额等）
  - 优先级设置（普通、中等、紧急）
  - 多文件附件上传支持（PDF、Word、Excel、图片等格式）
  - 审批流程配置（厂长选择、经理选择、CEO审批等）
  - 自动生成申请编号（格式：YYYYMMDD-XXXX）
  - 表单验证和数据校验
  - 申请书模板预览和PDF生成

#### 2.2 申请记录管理
- **申请记录页面**: `/application/record`
- **权限要求**: `application_record`
- **权限控制**:
  - **管理员**: 可以查看所有用户的申请记录
  - **申请提交者**: 只能查看自己创建的申请记录
  - **审批人**: 不能在此页面查看申请，应在待审批/已审批页面查看
- **功能特性**:
  - 申请记录列表展示
  - 高级搜索和筛选功能
  - 时间范围筛选（本周、本月、本年）
  - 搜索字段选择（申请内容、申请人）
  - 日期范围选择器
  - Excel导出功能
  - 申请金额合计显示
  - 统一分页组件
  - 申请详情查看（包含完整审批历史和电子签名）
  - 申请书模板查看和PDF下载

#### 2.3 待审核申请
- **待审核页面**: `/application/pending`
- **权限要求**: `pending_approval`
- **权限控制**:
  - **管理员**: 可以查看所有待审核申请（但不能审批）
  - **厂长**: 只能查看被选中需要厂长审批的申请
  - **总监**: 只能查看需要总监审批的申请
  - **经理**: 只能查看被选中需要经理审批的申请
  - **CEO**: 只能查看需要CEO审批的申请
- **功能特性**:
  - 待审核申请列表
  - 审批操作（通过/拒绝）
  - 审批意见填写
  - 电子签名自动关联
  - 审批流程跟踪
  - 批量审批操作
  - 搜索和分页功能
  - 申请书模板预览

#### 2.4 已审核申请
- **已审核页面**: `/application/approved`
- **权限要求**: `approved_applications`
- **权限控制**:
  - **管理员**: 可以查看所有已审核申请
  - **审批人**: 只能查看自己参与审批的申请（不包括自己创建的申请）
  - **申请提交者**: 应在申请记录页面查看自己的申请状态
- **功能特性**:
  - 已审核申请列表
  - 完整审批历史查看（包含电子签名）
  - 审批记录详情
  - 搜索和筛选功能
  - 统计分析功能
  - 申请书模板查看和下载

#### 2.5 申请历史
- **申请历史页面**: `/application/history`
- **功能特性**:
  - 历史申请记录查看
  - 申请删除功能
  - 搜索和分页支持
  - 申请状态跟踪

### 3. 生产排程管理

#### 3.1 排程仪表板
- **仪表板页面**: `/schedule/dashboard`
- **权限要求**: `schedule_view`
- **功能特性**:
  - 排程概览统计
  - 实时生产状态监控
  - 关键指标展示
  - 快速操作入口

#### 3.2 排程列表管理
- **排程列表页面**: `/schedule/list`
- **权限要求**: `schedule_view`
- **功能特性**:
  - 排程任务列表展示
  - 排程状态跟踪（计划中、进行中、已完成、已取消）
  - 优先级管理（低、中、高）
  - 搜索和筛选功能
  - 排程详情查看

#### 3.3 排程创建与编辑
- **创建页面**: `/schedule/create`
- **编辑页面**: `/schedule/edit`
- **权限要求**: `schedule_create`, `schedule_edit`
- **功能特性**:
  - 排程基本信息设置
  - 产品信息配置
  - 时间计划安排
  - 设备资源分配
  - 人员安排
  - 物料需求配置
  - 进度跟踪设置

#### 3.4 资源管理
- **资源管理页面**: `/schedule/resources`
- **权限要求**: `resource_manage`
- **功能特性**:
  - 设备资源管理
  - 人员资源配置
  - 物料资源跟踪
  - 资源利用率分析
  - 资源冲突检测

#### 3.5 排程报告
- **报告页面**: `/schedule/reports`
- **权限要求**: `schedule_report`
- **功能特性**:
  - 生产效率报告
  - 资源利用率报告
  - 排程完成率统计
  - 延期分析报告
  - 报告导出功能

### 4. 设备管理系统

#### 4.1 设备信息管理
- **设备信息页面**: `/equipment/info`
- **权限要求**: `equipment_info`
- **功能特性**:
  - 设备基本信息管理
  - 设备编码和分类
  - 设备位置和责任人
  - 设备规格参数
  - 设备状态跟踪
  - 厂区管理功能

#### 4.2 设备维护管理
- **维护管理页面**: `/equipment/maintenance`
- **权限要求**: `equipment_maintenance`
- **功能特性**:
  - **维修保养记录管理**: 完整的维修保养记录系统
    * 记录类型：维修、保养两种类型
    * 详细信息记录：设备信息、时间、操作人、描述、结果等
    * 故障程度分级：轻微、中等、严重、紧急四个等级
    * 状态管理：待处理、进行中、已完成、已取消
  - **高级搜索筛选**: 多维度数据筛选功能
    * 按记录类型、设备、操作人、时间范围筛选
    * 实时搜索，支持关键词模糊匹配
    * 智能筛选条件组合
  - **Excel导出功能**: 专业级表格导出
    * 完整表格结构：版本号、主标题、列标题、数据行
    * 时间格式：YYYY/M/D H:MM（不使用前导零）
    * 设备名称格式：设备编号(设备名称)
    * 中文类型显示：保养/维修
    * 自动行高计算，支持文本换行
  - **批量操作**: 提高操作效率
    * 批量删除记录
    * 批量状态更新
    * 批量导入功能（规划中）
  - **响应式设计**: 完美适配各种设备
    * 移动端友好界面
    * 自适应布局设计
    * 触摸操作优化
  - **实时统计**: 数据概览和分析
    * 记录数量统计
    * 分页显示控制
    * 数据加载状态提示

#### 4.3 设备健康评估
- **健康评估页面**: `/equipment/health`
- **权限要求**: `equipment_health`
- **功能特性**:
  - 设备健康度统计
  - 性能指标监控
  - 可靠性分析
  - 预测性维护
  - 健康度趋势分析
  - 评估报告生成

### 5. 质量管理系统

#### 5.1 检测报告上传
- **上传页面**: `/quality/upload`
- **权限要求**: `quality_upload`
- **功能特性**:
  - 多格式文件上传（Excel、PDF、Word）
  - 报告信息填写（标题、类型、日期等）
  - 检测详情记录
  - 自动报告编号生成
  - 邮件通知功能
  - 文件大小和数量限制

#### 5.2 检测报告管理
- **报告列表页面**: `/quality/list`
- **权限要求**: `quality_view`
- **功能特性**:
  - 报告列表展示
  - 搜索和筛选功能
  - 报告详情查看
  - 文件下载功能
  - 报告编辑和删除
  - 状态管理（已发布/草稿）

### 6. 系统监控与管理

#### 6.1 系统主页
- **主页页面**: `/dashboard`
- **功能特性**:
  - 系统概览仪表板
  - 快速导航入口
  - 关键指标展示
  - 最新动态显示

#### 6.2 系统健康监控
- **API端点**: `/api/system/health`, `/api/system/monitor`
- **功能特性**:
  - 系统运行状态监控
  - 数据库健康检查
  - 内存和CPU使用率
  - 系统性能指标
  - 错误率统计

#### 6.3 性能优化
- **API端点**: `/api/system/performance`, `/api/system/optimization`
- **功能特性**:
  - 性能统计分析
  - 慢查询检测
  - 优化建议生成
  - 缓存统计信息
  - 系统资源使用分析

## 🔐 权限系统

### 权限架构设计

系统采用**基于角色和权限的混合授权模式（RBAC + PBAC）**，提供多层次的安全控制：

#### 🏗️ 架构层次
1. **认证层**: JWT令牌验证 + 用户状态检查
2. **授权层**: 角色检查 + 细粒度权限控制
3. **数据层**: SQLite存储 + JSON权限数组
4. **中间件**: 三层权限控制（认证、角色、权限）

#### 🛡️ 安全特性
- **多层权限验证**: 前端 + 后端双重权限检查
- **权限模板管理**: 预设权限模板，快速分配权限
- **管理员特权**: admin角色自动拥有所有权限
- **权限继承**: 支持权限数组，灵活权限组合
- **会话管理**: JWT令牌自动过期，sessionStorage安全存储
- **统一认证**: 全系统使用sessionStorage，避免存储不一致问题

### 权限分类

#### 申请管理权限
- `new_application`: 新建申请权限
- `application_record`: 申请记录查看权限
- `pending_approval`: 待审核申请权限
- `approved_applications`: 已审核申请权限

#### 用户管理权限
- `view_users`: 查看用户列表权限
- `create_user`: 创建新用户权限
- `edit_user`: 编辑用户信息权限
- `delete_user`: 删除用户权限
- `manage_permissions`: 管理用户权限权限

#### 生产排程权限
- `schedule_view`: 排程查看权限
- `schedule_create`: 排程创建权限
- `schedule_edit`: 排程编辑权限
- `schedule_delete`: 排程删除权限
- `schedule_execute`: 排程执行权限
- `resource_manage`: 资源管理权限
- `schedule_report`: 排程报告权限

#### 设备管理权限
- `equipment_manage`: 设备管理权限
- `equipment_info`: 设备信息权限
- `equipment_maintenance`: 设备维护权限
- `equipment_health`: 设备健康评估权限

#### 质量管理权限
- `quality_upload`: 检测报告上传权限
- `quality_view`: 检测报告查看权限
- `quality_download`: 文件下载权限
- `quality_manage`: 质量管理权限

#### 文件管理权限
- `file_upload`: 文件上传权限
- `file_view`: 文件查看权限
- `file_download`: 文件下载权限
- `file_manage`: 文件管理权限
- `file_confirm`: 文件确认权限

#### 系统管理权限
- `user_settings`: 用户设置权限

### 权限模板管理

#### 预置权限模板
系统提供多个预置权限模板，方便快速分配用户权限：

1. **标准用户权限**
   - 权限: `new_application`, `application_record`, `user_settings`
   - 适用: 普通员工的标准权限配置

2. **审批人员权限**
   - 权限: `pending_approval`, `approved_applications`, `user_settings`
   - 适用: 具有审批权限的管理人员

3. **只读用户权限**
   - 权限: `application_record`, `approved_applications`, `user_settings`
   - 适用: 只需要查看权限的用户

4. **质量检测员权限**
   - 权限: `quality_upload`, `quality_view`, `quality_download`, `user_settings`
   - 适用: 质量检测人员，可以上传、查看和下载检测报告

5. **质量管理员权限**
   - 权限: `quality_upload`, `quality_view`, `quality_download`, `quality_manage`, `user_settings`
   - 适用: 质量管理人员，拥有完整的质量管理权限

#### 管理员权限
管理员（admin角色）自动拥有所有系统权限，包括：
- 所有申请管理权限
- 所有用户管理权限
- 所有生产排程权限
- 所有设备管理权限
- 所有质量管理权限
- 所有文件管理权限
- 用户设置权限

#### 权限控制实现

##### 后端权限控制
- **路由级权限**: 所有敏感API都有权限中间件保护
- **控制器级权限**: 在业务逻辑中进行二次权限验证
- **数据级权限**: 根据用户权限过滤返回数据

##### 前端权限控制
- **页面级权限**: 页面加载时检查用户权限
- **组件级权限**: 根据权限显示/隐藏功能组件
- **菜单级权限**: 侧边栏菜单根据权限动态生成

##### 权限验证流程
1. **用户登录**: 验证用户名密码，生成JWT令牌
2. **令牌验证**: 每次请求验证JWT令牌有效性
3. **权限检查**: 根据请求的资源检查用户权限
4. **访问控制**: 允许或拒绝用户访问请求的资源

## 🔧 系统修复记录

### 最新修复 (2025-07-29)

#### 电子签名显示问题修复

**问题描述**
- 申请书模板中电子签名无法显示，出现403权限错误
- 用户正常登录后仍然无法查看签名

**根本原因**
- 前端组件使用localStorage获取认证token
- 登录系统使用sessionStorage存储认证token
- 存储位置不一致导致token获取失败

**解决方案**
```javascript
// 修改前：使用localStorage
const authToken = localStorage.getItem('authToken');

// 修改后：使用sessionStorage
const authToken = sessionStorage.getItem('authToken');
```

**修复范围**
- SignatureDisplay.js组件
- ApplicationTemplate.js组件
- 统一全系统使用sessionStorage存储认证信息

#### 路由优化修复

**问题描述**
- 用户路由存在匹配冲突
- 通用路由`/:id`拦截了具体路由`/:id/signature`

**解决方案**
- 重新组织路由顺序，具体路由优先于通用路由
- 确保签名API路由正确匹配

### 历史修复 (2025-01-28)

#### 权限控制修复

**问题描述**
用户添加了权限后能看到页面，但点击相关功能时出现"您没有执行此操作的权限"错误。

**根本原因**
前端页面权限检查与后端API权限要求不匹配：
- **新建申请页面**：只需要 `new_application` 权限
- **获取厂长/经理列表API**：需要 `view_users` 权限
- **结果**：用户能访问页面但API调用失败

**解决方案**
修改 `/users/factory-managers` 和 `/users/managers` API的权限检查：
```javascript
// 修改前：只允许view_users权限
checkPermission('view_users')

// 修改后：允许用户管理或申请创建权限
checkPermission(['view_users', 'new_application'])
```

**修复逻辑**
创建申请时需要选择审批人，因此有 `new_application` 权限的用户应该能够获取厂长和经理列表。

## 📊 数据库设计

### 核心数据表

#### 用户表 (users)
- 用户基本信息（ID、用户名、密码、角色等）
- 部门信息和联系方式
- 权限配置和状态管理
- 登录记录和活跃时间

#### 申请表 (applications)
- 申请基本信息（编号、申请人、部门、内容等）
- 申请类型和优先级
- 审批流程配置
- 状态跟踪和时间记录

#### 审批记录表 (application_approvals)
- 审批人信息和角色
- 审批结果和意见
- 审批时间和流程阶段

#### 生产排程表 (schedules)
- 排程基本信息（标题、产品、数量等）
- 时间计划和状态管理
- 资源分配（设备、人员、物料）
- 进度跟踪和备注信息

#### 设备表 (equipment)
- 设备基本信息（编码、名称、位置等）
- 设备规格和状态
- 责任人和厂区信息

#### 厂区表 (factories)
- 厂区基本信息
- 厂区描述和管理信息

#### 质量报告表 (quality_reports)
- 报告基本信息（标题、编号、类型等）
- 检测详情和结论
- 状态管理和时间记录

#### 质量报告文件表 (quality_report_files)
- 文件基本信息（名称、路径、大小等）
- 文件类型和上传信息

#### 权限模板表 (permission_templates)
- 模板基本信息（名称、描述）
- 权限配置和内置标识

#### 系统日志表 (system_logs)
- 操作日志记录
- 错误日志跟踪
- 业务事件记录

## 🎨 用户界面设计

### 统一设计语言

#### 色彩系统
- **主色调**: 蓝色系 (#3B82F6, #1E40AF)
- **辅助色**: 绿色、橙色、紫色等功能性颜色
- **中性色**: 灰色系用于文本和背景

#### 组件设计
- **按钮**: 圆角设计，渐变背景，悬停效果
- **表单**: 现代化输入框，清晰的标签和验证提示
- **卡片**: 阴影效果，圆角边框，层次化布局
- **导航**: 侧边栏导航，响应式设计

#### 分页组件
- **统一分页**: 所有列表页面使用统一的分页组件
- **页码显示**: 智能省略号显示，当前页蓝色高亮
- **记录统计**: 左侧显示记录统计信息
- **响应式**: 适配桌面和移动设备

### 页面布局

#### 申请记录页面特色功能
- **高级搜索**: 时间筛选、字段选择、关键词搜索
- **日期选择器**: 可展开的日期范围选择
- **Excel导出**: 支持筛选结果导出
- **金额合计**: 实时计算显示合计金额
- **表格设计**: 9列信息展示，操作按钮集成

#### 申请详情模态框
- **现代化设计**: 渐变背景，卡片化布局
- **标签页导航**: 基本信息、申请记录、附件管理
- **交互效果**: 悬停动画，平滑过渡
- **响应式**: 完全适配移动端

## 🔧 技术特性

### 前端技术栈
- **Vue.js 3**: 组合式API，响应式数据
- **组件化**: 模块化组件设计，可复用性强
- **响应式**: 适配各种屏幕尺寸
- **现代化CSS**: Flexbox、Grid布局，CSS变量

### 后端技术栈
- **Node.js**: 高性能JavaScript运行时
- **Express.js**: 轻量级Web框架
- **SQLite**: 嵌入式数据库，易于部署
- **JWT**: 安全的用户认证机制

### 文件管理
- **本地存储**: 文件存储在服务器本地
- **目录结构**: 按功能和时间组织文件
- **文件安全**: 权限控制和访问验证
- **原文件名**: 保留用户上传的原始文件名

### 系统监控
- **健康检查**: 实时监控系统运行状态
- **性能统计**: CPU、内存、数据库性能
- **错误跟踪**: 自动记录和分析错误
- **优化建议**: 基于监控数据提供优化建议

## 🚀 部署与配置

### 系统要求
- **操作系统**: Windows、Linux、macOS
- **Node.js**: 版本14.0或更高
- **内存**: 最低2GB RAM
- **存储**: 最低10GB可用空间

### 网络配置
- **本地访问**: localhost:3000
- **局域网访问**: 支持通过IP地址访问
- **端口配置**: 默认3000端口，可配置
- **HTTPS**: 支持SSL证书配置

### 文件存储配置
- **上传目录**: backend/uploads/
- **质量报告**: backend/uploads/quality-reports/YYYY/MM/DD/
- **申请附件**: backend/uploads/applications/
- **文件限制**: 单文件10MB，总数限制可配置

## 📈 系统优势

### 业务价值
- **效率提升**: 自动化申请流程，减少人工处理时间
- **规范管理**: 标准化的申请和审批流程
- **数据追溯**: 完整的操作记录和审批历史
- **权限控制**: 精细化的权限管理，确保数据安全

### 技术优势
- **易于部署**: 单机部署，无需复杂配置
- **高性能**: 轻量级架构，响应速度快
- **可扩展**: 模块化设计，易于功能扩展
- **用户友好**: 现代化界面，操作简单直观

### 维护优势
- **日志完整**: 详细的操作和错误日志
- **监控完善**: 实时系统状态监控
- **备份简单**: SQLite数据库易于备份
- **更新便捷**: 支持在线更新和配置

## 📝 使用说明

### 管理员操作
1. **用户管理**: 创建用户账号，分配权限
2. **权限配置**: 使用权限模板或自定义权限
3. **系统监控**: 查看系统运行状态和性能
4. **数据维护**: 定期备份数据，清理日志

### 普通用户操作
1. **登录系统**: 使用分配的账号密码登录
2. **提交申请**: 填写申请信息，上传附件
3. **查看状态**: 跟踪申请审批进度
4. **下载文件**: 下载相关文档和报告

### 审批人员操作
1. **审批申请**: 查看待审核申请，进行审批
2. **填写意见**: 提供详细的审批意见
3. **流程控制**: 根据申请类型执行相应流程
4. **历史查询**: 查看历史审批记录

---

## 🆕 最新更新记录

### 2025-07-29 最新更新内容

#### 🔧 电子签名系统完善
- **签名显示修复**: 彻底解决了申请书模板中电子签名无法显示的问题
- **认证统一**: 统一使用sessionStorage存储认证信息，确保系统一致性
- **路由优化**: 优化了用户路由顺序，解决路由匹配冲突问题
- **权限简化**: 电子签名查看无需特殊权限，所有认证用户都可查看

#### 💡 技术改进
- **代码整洁**: 移除了所有调试代码，提升代码质量
- **错误处理**: 增强了JWT认证错误处理机制
- **性能优化**: 优化了签名获取和显示性能
- **文档更新**: 完善了README和功能文档

### 2025-07-22 历史更新内容

#### 🔧 维修保养记录管理系统重大优化

**Excel导出功能完善**
- ✅ **专业表格格式**: 实现1:1复刻专业表格格式
  - 精确的行高列宽控制（基于实际测量的转换系数）
  - 完整的表格结构：版本号SO4-09406 R:1.0、主标题、列标题、数据行
  - 列宽精确控制：开始时间3.5cm、结束时间3.5cm、机台名称6.5cm等
  - 动态行高计算，支持文本自动换行

- ✅ **时间格式优化**: 修复时间显示问题
  - 正确的时间格式：YYYY/M/D H:MM（月份、日期、小时不使用前导零）
  - 修复时间字段映射问题，确保与数据库结构一致
  - 支持日期+时间组合显示，解决"Invalid Date"问题

- ✅ **数据显示完善**: 确保数据完整性
  - 设备名称格式：设备编号(设备名称)
  - 中文类型显示：保养/维修
  - 修复字段映射问题，确保所有数据正确显示

**用户界面优化**
- ✅ **高级搜索布局改进**: 提升用户体验
  - 重置按钮移至结束日期旁边，操作更便捷
  - 删除重复的记录数量显示，界面更简洁
  - 调整网格布局为3列，使重置按钮与日期字段对齐

- ✅ **页面标题统一**: 品牌形象一致性
  - 统一设备管理页面标题格式：页面功能 - Makrite管理系统
  - 设备信息管理、维修/保养记录管理标题格式统一
  - 确保所有设备管理页面品牌标识一致

**技术改进**
- ✅ **代码质量提升**: 优化代码结构和性能
  - 修复Excel导出字段映射问题
  - 完善时间格式化函数，支持多种时间格式
  - 优化数据处理逻辑，确保数据准确性

#### 📊 功能统计
- **新增功能**: 专业级Excel导出
- **优化功能**: 高级搜索界面、时间格式化
- **修复问题**: 数据显示、字段映射、时间格式
- **代码提交**: 6个主要提交，涵盖功能完善和问题修复

---

## 📊 系统统计

- **总功能模块**: 7个已实现，2个规划中
- **核心组件**: 100+ 个前端组件
- **API接口**: 50+ 个RESTful接口
- **数据表**: 20+ 个核心数据表
- **权限控制**: 20+ 种细粒度权限
- **最新修复**: 电子签名显示、认证统一、路由优化

---

*本文档最后更新时间: 2025-07-29*
*系统版本: V2.0.1*
*维护状态: 活跃开发中*
