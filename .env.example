# ========================================
# 环境配置示例文件
# ========================================
# 使用方法：
# 1. 复制此文件为 .env：cp .env.example .env
# 2. 根据您的环境需要修改配置值
# 3. 重启应用使配置生效
# ========================================

# ========================================
# 🌍 应用环境配置
# ========================================
# 应用运行环境，影响日志级别、错误处理、性能监控等
# development: 开发环境，详细日志，错误堆栈显示
# production: 生产环境，精简日志，错误信息脱敏
# test: 测试环境，用于自动化测试
NODE_ENV=development

# ========================================
# 🌐 服务器配置
# ========================================
# 服务器监听端口，默认5050
# 确保端口未被其他应用占用
PORT=5050

# 服务器监听地址
# localhost: 仅本地访问
# 0.0.0.0: 允许外部访问（生产环境需谨慎）
HOST=localhost

# ========================================
# 🗄️ 数据库配置
# ========================================
# SQLite数据库文件路径
# 相对路径基于项目根目录
# 确保目录存在且有读写权限
DB_PATH=./database/application_system.db

# ========================================
# 📝 日志配置
# ========================================
# 日志级别：error, warn, info, debug
# error: 仅错误信息
# warn: 错误和警告信息
# info: 错误、警告和一般信息
# debug: 所有信息（开发环境推荐）
LOG_LEVEL=info

# 是否启用文件日志
# true: 日志保存到文件
# false: 仅控制台输出
LOG_FILE_ENABLED=true

# 是否启用控制台日志
# true: 在控制台显示日志
# false: 仅文件输出
LOG_CONSOLE_ENABLED=true

# ========================================
# 🔐 安全配置
# ========================================
# JWT密钥，用于生成和验证用户令牌
# 生产环境必须使用强密钥（建议32位以上随机字符串）
# 可使用命令生成：node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=your-secret-key-here

# JWT令牌过期时间
# 格式：数字+单位 (s=秒, m=分钟, h=小时, d=天)
# 例：30m=30分钟, 24h=24小时, 7d=7天
JWT_EXPIRES_IN=24h

# ========================================
# 🚦 速率限制配置
# ========================================
# 速率限制时间窗口（毫秒）
# 900000 = 15分钟
# 在此时间窗口内限制请求数量
RATE_LIMIT_WINDOW_MS=900000

# 时间窗口内最大请求数
# 超过此数量将返回429错误
# 根据服务器性能和用户数量调整
RATE_LIMIT_MAX_REQUESTS=100

# 登录尝试次数限制
# 防止暴力破解攻击
# 建议设置为3-10次
RATE_LIMIT_LOGIN_MAX=5

# ========================================
# 🛡️ IP访问控制配置
# ========================================
# IP白名单配置（逗号分隔，留空表示不启用IP限制）
# 支持多种格式：
# - 单个IP：127.0.0.1,*************
# - IP段(CIDR)：***********/24,10.0.0.0/8
# - IP范围：***********-*************
# - 本地访问：localhost,127.0.0.1
# 示例：IP_WHITELIST=127.0.0.1,***********/24,**********-**********
IP_WHITELIST=

# ========================================
# 📁 文件上传配置
# ========================================
# 单个文件最大大小
# 支持格式：10mb, 50mb, 1gb
# 根据服务器存储和带宽调整
UPLOAD_MAX_SIZE=10mb

# 文件上传存储路径
# 相对路径基于项目根目录
# 确保目录存在且有读写权限
UPLOAD_PATH=./uploads

# ========================================
# 💾 缓存配置
# ========================================
# 短期缓存时间（毫秒）
# 30000 = 30秒，适用于频繁变化的数据
CACHE_TTL_SHORT=30000

# 中期缓存时间（毫秒）
# 300000 = 5分钟，适用于一般数据
CACHE_TTL_MEDIUM=300000

# 长期缓存时间（毫秒）
# 1800000 = 30分钟，适用于相对稳定的数据
CACHE_TTL_LONG=1800000

# 缓存最大条目数
# 超过此数量将删除最旧的缓存
# 根据服务器内存调整
CACHE_MAX_ENTRIES=1000

# ========================================
# 📊 性能监控配置
# ========================================
# 慢查询阈值（毫秒）
# 超过此时间的数据库查询将被记录
# 用于优化数据库性能
PERFORMANCE_SLOW_QUERY_THRESHOLD=100

# 慢请求阈值（毫秒）
# 超过此时间的HTTP请求将被记录
# 用于优化接口性能
PERFORMANCE_SLOW_REQUEST_THRESHOLD=1000

# 内存使用率告警阈值（百分比）
# 超过此值将触发内存使用告警
# 建议设置为80-90
PERFORMANCE_MEMORY_THRESHOLD=85

# CPU使用率告警阈值（百分比）
# 超过此值将触发CPU使用告警
# 建议设置为70-90
PERFORMANCE_CPU_THRESHOLD=80

# ========================================
# 🛡️ 安全日志配置
# ========================================
# 是否启用安全日志
# true: 记录敏感操作、登录尝试等
# false: 禁用安全日志
SECURITY_LOG_ENABLED=true

# 是否仅在生产环境记录详细安全日志
# true: 开发环境简化日志，生产环境详细日志
# false: 所有环境都记录详细日志
SECURITY_LOG_PRODUCTION_ONLY=true

# ========================================
# 🔍 系统监控配置
# ========================================
# 是否启用系统监控
# true: 监控CPU、内存、磁盘使用情况
# false: 禁用系统监控
SYSTEM_MONITOR_ENABLED=true

# 系统指标收集间隔（毫秒）
# 30000 = 30秒
# 间隔越短监控越精确，但消耗更多资源
SYSTEM_MONITOR_INTERVAL=30000

# 监控数据清理间隔（毫秒）
# 300000 = 5分钟
# 定期清理旧的监控数据，避免内存泄漏
SYSTEM_MONITOR_CLEANUP_INTERVAL=300000

# ========================================
# 🔗 数据库连接池配置
# ========================================
# 最小连接数
# 应用启动时创建的连接数
# 建议设置为1-5
DB_POOL_MIN_CONNECTIONS=2

# 最大连接数
# 同时允许的最大数据库连接数
# 根据数据库性能和并发需求调整
DB_POOL_MAX_CONNECTIONS=10

# 获取连接超时时间（毫秒）
# 30000 = 30秒
# 超过此时间未获取到连接将报错
DB_POOL_ACQUIRE_TIMEOUT=30000

# 连接空闲超时时间（毫秒）
# 300000 = 5分钟
# 空闲超过此时间的连接将被关闭
DB_POOL_IDLE_TIMEOUT=300000

# ========================================
# 💡 配置建议
# ========================================
# 开发环境建议：
# - NODE_ENV=development
# - LOG_LEVEL=debug
# - SECURITY_LOG_PRODUCTION_ONLY=true
# - 较宽松的性能阈值
#
# 生产环境建议：
# - NODE_ENV=production
# - LOG_LEVEL=info
# - 使用强JWT密钥
# - 严格的速率限制
# - 较严格的性能阈值
#
# 测试环境建议：
# - NODE_ENV=test
# - LOG_LEVEL=warn
# - 禁用某些监控功能
# ========================================
