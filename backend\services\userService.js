/**
 * 用户服务
 * 处理用户相关的业务逻辑
 * 使用SQLite数据库
 */

const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');
const config = require('../config');
const { readJsonFile, writeJsonFile, ensureDirectoryExists } = require('../utils/fileSystem');
const userRepository = require('../database/userRepository');
const logger = require('../utils/logger');

/**
 * 读取所有用户
 * @returns {Array} 用户列表
 */
function getUsers() {
    try {
        return userRepository.findAll();
    } catch (error) {
        logger.error('获取用户列表失败:', error);
        return [];
    }
}

/**
 * 迁移用户数据到数据库（保留用于兼容性）
 * @param {Array} users - 用户列表
 */
function migrateUsersToFiles(users) {
    logger.info('用户数据迁移功能已移至数据库迁移脚本');
    // 此函数保留用于向后兼容，实际迁移由数据库迁移脚本处理
}

/**
 * 创建默认用户
 * @returns {Array} 默认用户列表
 */
function createDefaultUsers() {
    try {
        const defaultUsers = config.defaultUsers.map(user => {
            const newUser = {
                id: generateId(),
                usercode: user.username, // 用户代码使用配置中的username
                username: user.name,     // 用户名使用配置中的name
                password: bcrypt.hashSync(user.password, 10),
                role: user.role,
                department: user.department,
                active: true,
                permissions: []
            };

            // 保存到数据库
            return userRepository.create(newUser);
        });

        return defaultUsers;
    } catch (error) {
        logger.error('创建默认用户失败:', error);
        return [];
    }
}

/**
 * 读取用户数据（不再自动创建默认用户）
 * @returns {Array} 用户列表
 */
function readUsers() {
    try {
        return getUsers();
    } catch (error) {
        logger.error('读取用户数据失败:', error);
        return [];
    }
}

/**
 * 初始化系统用户（仅在系统首次启动时调用）
 * @returns {Array} 用户列表
 */
function initializeSystemUsers() {
    try {
        const users = getUsers();
        if (users.length === 0) {
            logger.info('系统首次启动，创建默认用户');
            return createDefaultUsers();
        }
        // 只在详细日志模式下显示用户初始化信息
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info(`系统已有 ${users.length} 个用户，跳过默认用户创建`);
        }
        return users;
    } catch (error) {
        logger.error('初始化系统用户失败:', error);
        return [];
    }
}

/**
 * 保存用户数据（批量更新，保留用于兼容性）
 * @param {Array} users - 用户列表
 * @returns {boolean} 是否成功
 */
function writeUsers(users) {
    try {
        // 对于批量操作，我们逐个更新用户
        for (const user of users) {
            if (user.id) {
                // 更新现有用户
                userRepository.update(user.id, user);
            } else {
                // 创建新用户
                user.id = generateId();
                userRepository.create(user);
            }
        }
        return true;
    } catch (error) {
        logger.error('批量保存用户数据失败:', error);
        return false;
    }
}

/**
 * 根据ID查找用户
 * @param {string} id - 用户ID
 * @returns {Object|null} 用户对象或null
 */
function getUserById(id) {
    try {
        // 确保ID是字符串
        const userId = String(id);
        logger.debug(`尝试获取用户ID: ${userId}`);

        return userRepository.findById(userId);
    } catch (error) {
        logger.error(`根据ID查找用户失败 (${id}):`, error);
        return null;
    }
}

/**
 * 根据用户名查找用户
 * @param {string} username - 用户名
 * @returns {Object|null} 用户对象或null
 */
function getUserByUsername(username) {
    try {
        return userRepository.findByUsername(username);
    } catch (error) {
        logger.error(`根据用户名查找用户失败 (${username}):`, error);
        return null;
    }
}

/**
 * 根据用户代码查找用户
 * @param {string} usercode - 用户代码
 * @returns {Object|null} 用户对象或null
 */
function getUserByUsercode(usercode) {
    try {
        return userRepository.findByUsercode(usercode);
    } catch (error) {
        logger.error(`根据用户代码查找用户失败 (${usercode}):`, error);
        return null;
    }
}

/**
 * 验证用户凭据
 * @param {string} usernameOrCode - 用户名或用户代码
 * @param {string} password - 密码
 * @returns {Object|null} 用户对象(不含密码)或null
 * @throws {Error} 如果用户被禁用
 */
function authenticateUser(usernameOrCode, password) {
    try {
        // 先尝试按用户代码查找
        let user = getUserByUsercode(usernameOrCode);

        // 如果找不到，再尝试按用户名查找
        if (!user) {
            user = getUserByUsername(usernameOrCode);
        }

        if (!user) {
            return null;
        }

        const isPasswordValid = bcrypt.compareSync(password, user.password);
        if (!isPasswordValid) {
            return null;
        }

        // 检查用户是否被禁用
        if (user.active === false) {
            throw new Error('账户已被禁用，请联系管理员');
        }

        // 更新最后登录时间
        userRepository.updateLastLogin(user.id);

        // 不返回密码
        const { password: _, ...userWithoutPassword } = user;
        return userWithoutPassword;
    } catch (error) {
        if (error.message === '账户已被禁用，请联系管理员') {
            throw error;
        }
        logger.error('用户认证失败:', error);
        return null;
    }
}

/**
 * 生成JWT令牌
 * @param {Object} user - 用户对象
 * @returns {string} JWT令牌
 */
function generateToken(user) {
    return jwt.sign(
        {
            id: user.id,
            username: user.username,
            usercode: user.usercode,
            role: user.role,
            permissions: user.permissions || [],
            active: user.active !== undefined ? user.active : true
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
    );
}

/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @returns {Object} 创建的用户(不含密码)
 */
function createUser(userData) {
    try {
        // 检查用户代码是否已存在
        if (userRepository.isUsercodeExists(userData.usercode)) {
            throw new Error('用户代码已存在');
        }

        const newUser = {
            id: generateId(), // 使用新的10位数字ID生成方法
            usercode: userData.usercode,
            username: userData.username,
            password: bcrypt.hashSync(userData.password, 10),
            role: userData.role || 'user',
            department: userData.department || '',
            email: userData.email || '',
            active: userData.active !== undefined ? userData.active : true,
            permissions: userData.permissions || []
        };

        // 保存到数据库
        const createdUser = userRepository.create(newUser);

        // 不返回密码
        const { password, ...userWithoutPassword } = createdUser;
        return userWithoutPassword;
    } catch (error) {
        logger.error('创建用户失败:', error);
        throw error;
    }
}

/**
 * 生成10位纯数字唯一ID
 * @returns {string} 10位数字UID
 */
function generateId() {
    return userRepository.generateId();
}

/**
 * 读取单个用户文件（保留用于兼容性）
 * @param {string} userId - 用户ID
 * @returns {Object|null} 用户对象或null
 */
function readUserFile(userId) {
    return getUserById(userId);
}

/**
 * 写入单个用户文件（保留用于兼容性）
 * @param {Object} user - 用户对象
 * @returns {boolean} 是否成功
 */
function writeUserFile(user) {
    try {
        if (!user || !user.id) {
            logger.error('写入用户失败: 用户对象或ID为空');
            return false;
        }

        // 确保用户对象有permissions字段
        if (!user.permissions) {
            user.permissions = [];
        }

        // 检查用户是否存在，决定是创建还是更新
        const existingUser = userRepository.findById(user.id);
        if (existingUser) {
            userRepository.update(user.id, user);
        } else {
            userRepository.create(user);
        }

        return true;
    } catch (error) {
        logger.error(`写入用户失败: ${error.message}`);
        return false;
    }
}

/**
 * 获取管理员用户数量
 * @returns {number} 管理员数量
 */
function getAdminCount() {
    try {
        const users = userRepository.findAll();
        return users.filter(user => user.role === 'admin' && user.active).length;
    } catch (error) {
        logger.error('获取管理员数量失败:', error);
        throw error;
    }
}

/**
 * 检查用户是否有未完成的重要业务
 * @param {string} userId - 用户ID
 * @returns {Object} 检查结果
 */
function checkUserActiveBusiness(userId) {
    try {
        const databaseManager = require('../database/database');
        const db = databaseManager.db;

        if (!db) {
            throw new Error('数据库连接不可用');
        }

        // 检查未完成的申请（作为申请人）
        const pendingApplications = db.prepare(`
            SELECT COUNT(*) as count
            FROM applications
            WHERE user_id = ? AND status IN ('pending', 'in_progress')
        `).get(userId);

        // 检查待审批的申请（作为审批人）
        const pendingApprovals = db.prepare(`
            SELECT COUNT(*) as count
            FROM applications a
            WHERE a.status = 'pending'
            AND (
                a.selected_factory_managers LIKE '%"' || ? || '"%'
                OR a.selected_managers LIKE '%"' || ? || '"%'
            )
        `).get(userId, userId);

        // 检查最近上传的质量报告（30天内）
        let recentReports = { count: 0 };
        try {
            recentReports = db.prepare(`
                SELECT COUNT(*) as count
                FROM quality_reports
                WHERE uploaded_by = ?
                AND datetime(uploaded_at) > datetime('now', '-30 days')
            `).get(userId);
        } catch (e) {
            // 如果质量报告表不存在，忽略此检查
            if (!e.message.includes('no such table')) {
                throw e;
            }
        }

        return {
            hasActiveBusiness: pendingApplications.count > 0 || pendingApprovals.count > 0 || recentReports.count > 0,
            pendingApplications: pendingApplications.count,
            pendingApprovals: pendingApprovals.count,
            recentReports: recentReports.count
        };
    } catch (error) {
        logger.error(`检查用户活跃业务失败 (${userId}):`, error);
        // 如果检查失败，为了安全起见，假设有活跃业务
        return {
            hasActiveBusiness: true,
            error: error.message
        };
    }
}

/**
 * 删除用户及其关联数据
 * @param {string} userId - 用户ID
 * @returns {boolean} 是否成功
 */
function deleteUserFile(userId) {
    try {
        logger.info(`开始删除用户: ${userId}`);

        // 获取用户信息
        const user = userRepository.findById(userId);
        if (!user) {
            throw new Error('用户不存在');
        }

        // 1. 检查是否为最后一个管理员
        if (user.role === 'admin') {
            const adminCount = getAdminCount();
            if (adminCount <= 1) {
                throw new Error('不能删除最后一个管理员账户，系统至少需要保留一个管理员');
            }
        }

        // 2. 检查是否有未完成的重要业务
        const businessCheck = checkUserActiveBusiness(userId);
        if (businessCheck.hasActiveBusiness) {
            let errorMessage = '该用户有未完成的重要业务，请先处理完成后再删除：\n';
            if (businessCheck.pendingApplications > 0) {
                errorMessage += `• ${businessCheck.pendingApplications} 个未完成的申请\n`;
            }
            if (businessCheck.pendingApprovals > 0) {
                errorMessage += `• ${businessCheck.pendingApprovals} 个待审批的申请\n`;
            }
            if (businessCheck.recentReports > 0) {
                errorMessage += `• ${businessCheck.recentReports} 个最近上传的质量报告\n`;
            }
            if (businessCheck.error) {
                errorMessage += `• 业务检查出现错误: ${businessCheck.error}`;
            }
            throw new Error(errorMessage.trim());
        }

        // 3. 执行删除操作
        const result = userRepository.delete(userId);

        if (result) {
            logger.info(`用户删除成功: ${userId} (${user.username})`);
        } else {
            logger.warn(`用户删除失败，未找到用户: ${userId}`);
        }

        return result;
    } catch (error) {
        logger.error(`删除用户失败 (${userId}): ${error.message}`);

        // 如果是外键约束错误，提供更友好的错误信息
        if (error.message.includes('FOREIGN KEY constraint failed')) {
            throw new Error('无法删除用户，该用户存在关联数据。请先处理相关业务数据后再删除。');
        }

        throw error;
    }
}

/**
 * 哈希密码
 * @param {string} password - 明文密码
 * @returns {string} 哈希后的密码
 */
function hashPassword(password) {
    return bcrypt.hashSync(password, 10);
}

/**
 * 获取所有用户（包括邮箱信息）
 * @returns {Array} 用户列表
 */
function getAllUsers() {
    return readUsers();
}

module.exports = {
    readUsers,
    writeUsers,
    getAllUsers,
    getUserById,
    getUserByUsername,
    getUserByUsercode,
    authenticateUser,
    generateToken,
    createUser,
    generateId,
    hashPassword,
    readUserFile,
    writeUserFile,
    deleteUserFile,
    getAdminCount,
    checkUserActiveBusiness,
    migrateUsersToFiles,
    initializeSystemUsers
};
