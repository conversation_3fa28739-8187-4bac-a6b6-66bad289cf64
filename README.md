# 智能化企业管理系统 V2.0

[![Node.js](https://img.shields.io/badge/Node.js-14%2B-green.svg)](https://nodejs.org/)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.0-blue.svg)](https://vuejs.org/)
[![SQLite](https://img.shields.io/badge/SQLite-3.0-lightgrey.svg)](https://sqlite.org/)
[![AI](https://img.shields.io/badge/AI-Intelligent%20Scheduling-orange.svg)](https://github.com/Darrowyu/management-system-v2.0.0)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个基于 Node.js 和 Vue.js 的现代化智能企业管理系统，集成AI智能排产算法，提供完整的申请流程管理、智能生产排程、产能优化、质量管理和设备管理功能。

## ✨ 核心特性

- 🚀 **现代化架构**: 前后端分离，Vue.js 3 + Node.js + Express
- 🤖 **AI智能排产**: 5种优化策略算法，排程效率提升40%
- 🔐 **企业级安全**: JWT认证，RBAC权限控制，审计追踪
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🔄 **完整工作流**: 多级审批流程，实时状态跟踪
- 📊 **智能分析**: 实时监控，性能分析，预测性维护，图表可视化
- 🎨 **现代化UI**: Tailwind CSS，专业美观的界面
- 📁 **文件管理**: 多格式文件上传，安全存储
- 🌐 **高可用性**: 支持本地和局域网部署，高并发处理
- ⚡ **性能优化**: 连接池，缓存机制，性能提升200%

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端 (Node.js)  │    │ AI算法引擎       │    │  数据库 (SQLite) │
│                 │    │                 │    │                 │    │                 │
│ • 响应式界面     │◄──►│ • RESTful API   │◄──►│ • 智能排产       │◄──►│ • 数据持久化     │
│ • 组件化开发     │    │ • JWT 认证      │    │ • 产能优化       │    │ • 事务支持       │
│ • 状态管理       │    │ • 权限控制      │    │ • 交期预测       │    │ • 连接池优化     │
│ • 实时监控       │    │ • 性能监控      │    │ • 资源匹配       │    │ • 索引优化       │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 功能模块

### 已实现模块 (8个)

1. **用户认证与权限管理** ✅
   - JWT Token认证，RBAC权限控制
   - 权限模板，动态权限分配
   - 用户管理，审计追踪

2. **申请管理系统** ✅
   - 多级审批流程（厂长→总监→经理→CEO），实时状态跟踪
   - 电子签名集成，申请书模板，PDF生成
   - 邮件通知，多文件上传支持
   - 申请历史，完整审批记录
   - **最新修复**: 电子签名显示问题已完全解决，支持sessionStorage统一认证

3. **智能排产系统** ✅
   - 5种AI优化策略算法
   - 多方案对比，交期预测
   - 资源冲突检测，风险评估

4. **产品管理系统** ✅
   - 产品全生命周期管理
   - 工艺流程可视化
   - 产品编码，规格管理

5. **设备管理系统** ✅
   - 设备全生命周期管理，厂区管理
   - 产能配置，预测性维护
   - 设备状态监控，实时图表展示
   - 设备状态分布图，厂区设备分布图
   - 智能筛选，数据导入导出
   - **维修保养记录管理**: 完整的维修保养记录系统
     * 高级搜索筛选，支持多维度筛选
     * Excel导出功能
     * 实时数据统计，批量操作支持
     * 响应式设计，移动端友好界面

6. **质量管理系统** ✅
   - 多格式文件支持
   - 自动报告编号
   - 质量追溯，检测报告管理

7. **生产排程管理** ✅
   - 可视化排程界面
   - 资源分配优化
   - 进度跟踪，状态管理

8. **仓库管理系统** ✅
   - **物料成品管理**: 统一管理界面，支持物料和成品的增删改查操作
   - **二维码标识系统**: 自动生成和管理二维码，支持扫码快速操作
   - **库存实时监控**: 实时库存数据展示，智能预警和统计分析
   - **出入库操作**: 支持入库、出库、退库等完整业务流程
   - **操作记录查询**: 完整的操作历史记录和高级筛选功能
   - **报表生成分析**: 库存报表、交易报表、汇总报表和数据追溯
   - **权限控制管理**: 基于角色的权限控制，确保操作安全性

9. **系统日志管理** ✅
   - **现代化日志查看器**: 高级现代化UI设计，支持虚拟滚动和实时刷新
   - **智能搜索系统**: 实时搜索、搜索历史、快速搜索预设、高级搜索选项
   - **多维度过滤**: 日志级别过滤、时间范围过滤、快速时间选择
   - **文件管理功能**: 日志文件列表、删除确认、批量清理过期日志
   - **专业级功能**: 日志详情查看、一键复制导出、快捷键支持
   - **权限安全控制**: 仅限管理员访问，确保系统安全性

### 规划中模块 (1个)

10. **文档管理系统** 🔄
    - 电子签名，在线协作编辑
    - 版本控制，权限管理

## 🤖 AI智能特性

### 智能排产优化
- **5种优化策略**: 最早完成、高效率、负载均衡、成本优化、低风险
- **性能提升**: 排程效率提升40%，资源利用率提升35%
- **预测准确率**: 交期预测准确率95%

### 设备智能匹配
- **智能分析**: 技能匹配、效率评估、负载均衡
- **效率提升**: 生产效率提升25%，设备利用率提升30%

### 交期智能预测
- **数据驱动**: 历史数据分析、实时进度跟踪、风险评估
- **准确预测**: 预测准确率95%，提前预警风险

### 产能智能计算
- **动态计算**: 实时产能分析，设备效率评估
- **优化建议**: 自动生成产能优化方案
- **资源配置**: 智能资源分配，负载均衡

## 📋 系统要求

### 运行环境
- **Node.js**: 14.0 或更高版本
- **npm**: 6.0 或更高版本
- **操作系统**: Windows / Linux / macOS
- **内存**: 最低 2GB RAM
- **存储**: 最低 10GB 可用空间

### 浏览器支持
- Chrome 80+ (推荐)
- Firefox 75+
- Safari 13+
- Edge 80+
- 移动端浏览器支持

## 🚀 快速开始

### 安装

1. **克隆项目**
   ```bash
   git clone https://github.com/Darrowyu/management-system-v2.0.0
   cd management-system-v2.0.0
   ```

2. **安装依赖**
   ```bash
   # 安装所有依赖（推荐）
   npm run install:all

   # 或者分别安装
   npm install
   cd backend && npm install
   ```

3. **配置环境变量**（可选）
   ```bash
   # 复制环境变量配置文件到项目根目录
   cp .env.example .env

   # 编辑配置文件，设置邮件服务、IP访问控制等
   # 注意：系统使用根目录的.env文件，不需要在backend目录创建
   ```

### 运行

1. **快速启动**
   ```bash
   # 使用npm脚本启动（简洁模式，推荐）
   npm start

   # 或使用批处理文件（Windows）
   start-quiet.bat

   # 详细日志模式（调试时使用）
   set VERBOSE_LOGS=true && npm start
   # 或使用批处理文件
   start-verbose.bat
   ```

2. **访问系统**
   - 本地访问: http://localhost:3000
   - 局域网访问: http://[your-ip]:3000

### 默认账户

系统提供以下默认测试账户：

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 全部权限 |
| 普通用户 | user | user123 | 基础权限 |

> ⚠️ **安全提示**: 生产环境中请立即修改默认密码

### 权限系统

系统采用**基于角色和权限的混合授权模式（RBAC + PBAC）**，提供细粒度的权限控制：

#### 🔐 权限架构
- **认证层**: JWT令牌验证 + 用户状态检查
- **授权层**: 角色检查 + 细粒度权限控制
- **数据层**: SQLite存储 + JSON权限数组
- **中间件**: 三层权限控制（认证、角色、权限）

#### 📋 权限分类
**申请管理权限**
- `new_application` - 新建申请
- `application_record` - 申请记录查看
- `pending_approval` - 待审核申请查看
- `approved_applications` - 已审核申请查看

**用户管理权限**
- `view_users` - 查看用户列表
- `create_user` - 创建新用户
- `edit_user` - 编辑用户信息
- `delete_user` - 删除用户
- `manage_permissions` - 管理用户权限

**系统管理权限**
- `schedule_view/create/edit/delete` - 排程管理
- `equipment_manage/info/maintenance` - 设备管理
- `quality_upload/view/manage` - 质量管理
- `file_upload/view/manage` - 文件管理

**仓库管理权限**
- `warehouse_view` - 查看仓库信息
- `warehouse_inbound` - 入库操作权限
- `warehouse_outbound` - 出库操作权限
- `warehouse_return` - 退库操作权限
- `warehouse_manage` - 仓库管理权限

#### 🛡️ 安全特性
- **多层权限验证**: 前端 + 后端双重权限检查
- **权限模板管理**: 预设权限模板，快速分配权限
- **管理员特权**: admin角色自动拥有所有权限
- **权限继承**: 支持权限数组，灵活权限组合
- **会话管理**: JWT令牌自动过期，sessionStorage安全存储
- **统一认证**: 全系统使用sessionStorage，避免存储不一致问题

## 📁 项目结构
```
/
├── frontend/                       # 前端代码
│   ├── assets/                     # 静态资源
│   │   ├── css/                    # 样式文件
│   │   │   └── sidebar-scrollbar.css # 侧边栏滚动条样式
│   │   ├── images/                 # 图片资源
│   │   └── fonts/                  # 字体文件
│   ├── components/                 # 组件目录
│   │   ├── common/                 # 通用组件
│   │   │   └── Sidebar.js          # 侧边导航栏组件
│   │   ├── application/            # 申请相关组件
│   │   │   ├── ApplicationForm.js  # 申请表单组件
│   │   │   ├── ApplicationList.js  # 申请列表组件
│   │   │   ├── ApplicationDetail.js # 申请详情组件
│   │   │   ├── ApplicationTemplate.js # 申请书模板组件
│   │   │   ├── SignatureDisplay.js # 电子签名显示组件
│   │   │   ├── ApplicationRecordList.js # 申请记录列表组件
│   │   │   ├── PendingApplicationList.js # 待审核申请列表组件
│   │   │   └── ApprovedApplicationList.js # 已审核申请列表组件
│   │   ├── scheduling/             # 智能排产相关组件
│   │   │   ├── CapacityUtilization.js # 产能利用率组件
│   │   │   ├── DeliveryPredictionChart.js # 交期预测图表组件
│   │   │   ├── PlanComparisonCard.js # 方案对比卡片组件
│   │   │   ├── ProductionTimeline.js # 生产时间线组件
│   │   │   └── RiskAssessment.js   # 风险评估组件
│   │   ├── quality/                # 质量管理相关组件
│   │   │   ├── QualityReportForm.js # 检测报告上传表单组件
│   │   │   ├── QualityReportList.js # 检测报告列表组件
│   │   │   └── QualityReportDetail.js # 检测报告详情组件
│   │   └── user/                   # 用户相关组件
│   │       ├── UserList.js         # 用户列表组件
│   │       ├── UserSettingsForm.js # 用户设置表单组件
│   │       ├── PermissionManager.js # 权限管理组件
│   │       └── PermissionTemplateManager.js # 权限模板管理组件
│   ├── pages/                      # 页面HTML文件
│   │   ├── index.html              # 主页（重定向页面）
│   │   ├── login.html              # 登录页面
│   │   ├── dashboard.html          # 系统仪表板页面
│   │   ├── system-overview.html    # 系统功能展示页面
│   │   ├── application/            # 申请相关页面
│   │   │   ├── new.html            # 新建申请页面
│   │   │   ├── history.html        # 申请历史页面
│   │   │   ├── record.html         # 申请记录页面
│   │   │   ├── pending.html        # 待审核页面
│   │   │   └── approved.html       # 已审核页面
│   │   ├── schedule/               # 排程相关页面
│   │   │   ├── dashboard.html      # 排程总览页面
│   │   │   ├── list.html           # 排程计划页面
│   │   │   ├── create.html         # 新建排程页面
│   │   │   ├── edit.html           # 编辑排程页面
│   │   │   ├── resources.html      # 资源管理页面
│   │   │   └── reports.html        # 数据分析页面
│   │   ├── scheduling/             # 智能排产页面
│   │   │   └── intelligent.html    # 智能排产页面
│   │   ├── product/                # 产品管理页面
│   │   │   ├── management.html     # 产品管理页面
│   │   │   └── processes.html      # 工艺流程页面
│   │   ├── equipment/              # 设备管理页面
│   │   │   ├── info.html           # 设备信息页面
│   │   │   ├── health.html         # 设备健康页面
│   │   │   ├── maintenance.html    # 设备维护页面
│   │   │   └── capacity.html       # 设备产能页面
│   │   ├── operator/               # 操作员管理页面
│   │   │   └── skills.html         # 操作员技能页面
│   │   ├── admin/                  # 管理员页面
│   │   │   └── algorithm-tuning.html # 算法调优页面
│   │   ├── quality/                # 质量管理相关页面
│   │   │   ├── upload.html         # 检测报告上传页面
│   │   │   └── list.html           # 检测报告列表页面
│   │   └── user/                   # 用户相关页面
│   │       ├── management.html     # 用户管理页面
│   │       └── settings.html       # 用户设置页面
│   ├── js/                         # JavaScript资源
│   │   └── libs/                   # 本地化的第三方库
│   │       ├── vue.global.js       # Vue.js 3 前端框架
│   │       ├── axios.min.js        # Axios HTTP请求库
│   │       ├── chart.umd.js        # Chart.js 图表库
│   │       └── tailwindcss.js      # Tailwind CSS框架
│   └── scripts/                    # JS脚本文件
│       ├── api/                    # API请求模块
│       │   ├── auth.js             # 认证相关API
│       │   ├── application.js      # 申请相关API
│       │   ├── schedule.js         # 排程相关API
│       │   ├── scheduling.js       # 智能排产相关API
│       │   ├── product.js          # 产品管理相关API
│       │   ├── capacity.js         # 产能管理相关API
│       │   ├── equipment.js        # 设备管理相关API
│       │   ├── quality.js          # 质量管理相关API
│       │   ├── user.js             # 用户相关API
│       │   └── config.js           # API配置和拦截器
│       ├── common/                 # 公共代码库
│       │   ├── utils.js            # 通用工具函数库
│       │   ├── pageInit.js         # 页面初始化工具
│       │   └── debugUtils.js       # 调试工具函数
│       ├── utils/                  # 工具函数（已弃用，迁移至common）
│       ├── config.js               # 配置文件
│       ├── global.js               # 全局初始化脚本
│       ├── main.js                 # 主应用重定向逻辑
│       └── pages/                  # 页面脚本
│           ├── login.js            # 登录页面逻辑
│           ├── dashboard.js        # 系统仪表板逻辑
│           ├── system-overview.js  # 系统功能展示逻辑
│           ├── application/        # 申请相关页面脚本
│           │   ├── new.js          # 新建申请页面逻辑
│           │   ├── history.js      # 申请历史页面逻辑
│           │   ├── record.js       # 申请记录页面逻辑
│           │   ├── pending.js      # 待审核页面逻辑
│           │   └── approved.js     # 已审核页面逻辑
│           ├── schedule/           # 排程相关页面脚本
│           │   ├── dashboard.js    # 排程总览页面逻辑
│           │   ├── list.js         # 排程计划页面逻辑
│           │   ├── create.js       # 新建排程页面逻辑
│           │   ├── edit.js         # 编辑排程页面逻辑
│           │   ├── resources.js    # 资源管理页面逻辑
│           │   └── reports.js      # 数据分析页面逻辑
│           ├── scheduling/         # 智能排产页面脚本
│           │   └── intelligent.js  # 智能排产页面逻辑
│           ├── product/            # 产品管理页面脚本
│           │   ├── management.js   # 产品管理页面逻辑
│           │   └── processes.js    # 工艺流程页面逻辑
│           ├── equipment/          # 设备管理页面脚本
│           │   ├── info.js         # 设备信息页面逻辑
│           │   ├── maintenance.js  # 设备维护页面逻辑
│           │   └── capacity.js     # 设备产能页面逻辑
│           ├── operator/           # 操作员管理页面脚本
│           │   └── skills.js       # 操作员技能页面逻辑
│           ├── admin/              # 管理员页面脚本
│           │   └── algorithm-tuning.js # 算法调优页面逻辑
│           ├── quality/            # 质量管理相关页面脚本
│           │   ├── upload.js       # 检测报告上传页面逻辑
│           │   └── list.js         # 检测报告列表页面逻辑
│           └── user/               # 用户相关页面脚本
│               ├── management.js   # 用户管理页面逻辑
│               └── settings.js     # 用户设置页面逻辑
│
├── backend/                        # 后端代码
│   ├── server.js                   # 服务器入口文件
│   ├── package.json                # 后端依赖配置
│   ├── config/                     # 配置模块
│   │   └── index.js                # 配置文件
│   ├── algorithms/                 # AI算法模块
│   │   ├── CapacityCalculator.js   # 产能计算算法
│   │   ├── DeliveryPredictor.js    # 交期预测算法
│   │   ├── IntelligentScheduler.js # 智能排产算法
│   │   └── ResourceOptimizer.js    # 资源优化算法
│   ├── controllers/                # 控制器模块
│   │   ├── authController.js       # 认证控制器
│   │   ├── applicationController.js # 申请控制器
│   │   ├── qualityController.js    # 质量管理控制器
│   │   ├── userController.js       # 用户管理控制器
│   │   ├── productController.js    # 产品管理控制器
│   │   ├── capacityController.js   # 产能管理控制器
│   │   └── TuningController.js     # 算法调优控制器
│   ├── models/                     # 数据模型
│   │   ├── productModel.js         # 产品数据模型
│   │   └── capacityModel.js        # 产能数据模型
│   ├── middlewares/                # 中间件模块
│   │   ├── auth.js                 # 认证中间件
│   │   └── upload.js               # 文件上传中间件
│   ├── routes/                     # 路由模块
│   │   ├── index.js                # 路由索引
│   │   ├── authRoutes.js           # 认证路由
│   │   ├── applicationRoutes.js    # 申请路由
│   │   ├── qualityRoutes.js        # 质量管理路由
│   │   ├── productRoutes.js        # 产品管理路由
│   │   ├── capacityRoutes.js       # 产能管理路由
│   │   ├── scheduling.js           # 智能排产路由
│   │   ├── performance.js          # 性能监控路由
│   │   └── tuningRoutes.js         # 算法调优路由
│   ├── services/                   # 服务模块
│   │   ├── userService.js          # 用户服务
│   │   ├── applicationService.js   # 申请服务
│   │   ├── qualityService.js       # 质量管理服务
│   │   ├── productService.js       # 产品管理服务
│   │   ├── capacityService.js      # 产能管理服务
│   │   ├── SchedulingService.js    # 智能排产服务
│   │   └── scheduling/             # 排产算法服务
│   │       └── AlgorithmTuner.js   # 算法调优服务
│   ├── database/                   # 数据库模块
│   │   ├── database.js             # 数据库连接配置
│   │   ├── application_system.db   # SQLite数据库文件
│   │   ├── applicationRepository.js # 申请数据仓库
│   │   ├── userRepository.js       # 用户数据仓库
│   │   ├── productRepository.js    # 产品数据仓库
│   │   └── capacityRepository.js   # 产能数据仓库
│   ├── utils/                      # 工具模块
│   │   ├── fileSystem.js           # 文件系统工具
│   │   ├── signatureConverter.js   # 签名转换工具
│   │   ├── logger.js               # 日志记录工具
│   │   ├── ConcurrencyManager.js   # 并发管理工具
│   │   ├── PerformanceMonitor.js   # 性能监控工具
│   │   └── SchedulingCache.js      # 排产缓存工具
│   ├── uploads/                    # 上传文件存储目录
│   └── data/                       # 数据备份目录
│       ├── applications.json.backup # 申请数据备份
│       └── users.json.backup       # 用户数据备份
│
├── docs/                           # 项目文档
│   ├── SIDEBAR_NAVIGATION.md       # 侧边栏导航文档
│   ├── SYSTEM_OVERVIEW_UPDATE.md   # 系统功能更新说明
│   └── api/                        # API文档
│       └── intelligent-scheduling.md # 智能排产API文档
├── package.json                    # 项目根依赖配置
├── .gitignore                      # Git忽略文件
├── install.bat                     # 安装脚本
└── start.bat                       # 启动脚本
```

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue.js 3 (Composition API)
- **样式**: Tailwind CSS
- **图表库**: Chart.js (UMD版本，支持饼图、柱状图)
- **二维码**: QRCode.js + html5-qrcode (仓库管理二维码生成和扫描)
- **HTTP客户端**: Axios
- **模块化**: ES6 Modules
- **构建工具**: 原生 ES6，无需构建步骤

### 后端技术
- **运行时**: Node.js 14+
- **框架**: Express.js
- **数据库**: SQLite + better-sqlite3
- **认证**: JWT (JSON Web Tokens)
- **文件上传**: Multer
- **密码加密**: bcrypt
- **邮件服务**: Nodemailer
- **日志记录**: Winston
- **Excel处理**: ExcelJS

### AI算法引擎
- **智能排产**: 多策略优化算法
- **产能计算**: 动态产能分析算法
- **交期预测**: 机器学习预测模型
- **资源优化**: 智能资源匹配算法
- **风险评估**: 多维度风险分析

### 数据存储
- **主数据库**: SQLite (application_system.db)
- **仓库管理表**: warehouse_materials, warehouse_finished_products, warehouse_transactions, warehouse_qrcodes
- **连接池**: 数据库连接池优化
- **文件存储**: 本地文件系统
- **备份机制**: 自动JSON备份
- **事务支持**: SQLite事务
- **缓存机制**: 内存缓存优化

## ⚙️ 配置说明

### 环境变量配置

创建 `backend/.env` 文件：

```env
# 邮件服务配置（可选）
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
```

### Gmail邮件服务配置

1. **启用两步验证**
   - 登录 Google 账户
   - 进入"安全性"设置
   - 启用"两步验证"

2. **生成应用专用密码**
   - 在"两步验证"下选择"应用专用密码"
   - 选择"邮件"和设备类型
   - 生成16位密码并设置为 `EMAIL_PASS`

3. **测试邮件服务**
   ```bash
   cd backend
   node scripts/testEmailService.js
   ```

## 📊 数据库说明

### 数据库迁移
系统已完成从JSON文件到SQLite数据库的迁移：

- ✅ 数据库文件: `backend/database/application_system.db`
- ✅ 历史数据已完整迁移
- ✅ 原JSON文件已备份至 `backend/data/`
- ✅ 支持事务和数据完整性约束

### 核心数据表
- `users` - 用户信息和权限
- `applications` - 申请记录
- `application_approvals` - 审批记录
- `schedules` - 生产排程
- `equipment` - 设备信息
- `equipment_capabilities` - 设备产能配置
- `products` - 产品信息
- `production_processes` - 生产工艺流程
- `operators` - 操作员信息
- `operator_skills` - 操作员技能
- `quality_reports` - 质量报告
- `quality_report_files` - 质量报告文件
- `permission_templates` - 权限模板
- `factories` - 厂区信息

### 备份与恢复
```bash
# 备份数据库
cp backend/database/application_system.db backup/

# 查看数据库信息
sqlite3 backend/database/application_system.db ".tables"
```

## 🔧 开发指南

### 开发环境设置

1. **代码编辑器推荐**
   - VS Code + Vue Language Features 插件
   - WebStorm

2. **调试工具**
   - Chrome DevTools
   - Vue DevTools 浏览器扩展

3. **代码规范**
   - ESLint 配置
   - Prettier 代码格式化
   - Git hooks 预提交检查

### API 接口文档

系统提供完整的 RESTful API 接口，详细文档请参考：

- **[API文档总览](./docs/api/README.md)** - API概述、认证、错误处理
- **[认证API](./docs/api/auth.md)** - 用户登录、令牌验证
- **[申请管理API](./docs/api/applications.md)** - 申请CRUD、审批流程
- **[用户管理API](./docs/api/users.md)** - 用户管理、权限控制、电子签名
- **[设备管理API](./docs/api/equipment.md)** - 设备信息、维护记录、厂区管理
- **[质量管理API](./docs/api/quality.md)** - 检测报告、文件管理
- **[仓库管理API](./docs/api/warehouse.md)** - 物料成品管理、库存监控、二维码系统
- **[智能排产API](./docs/api/intelligent-scheduling.md)** - AI算法、优化策略
- **[系统监控API](./docs/api/system.md)** - 性能监控、健康检查

#### 快速参考 - 核心接口

#### 认证接口
```
POST   /api/auth/login           # 用户登录
GET    /api/auth/me              # 获取当前用户信息
POST   /api/auth/logout          # 用户登出
```

#### 申请管理接口
```
GET    /api/applications         # 获取申请列表
POST   /api/applications         # 创建新申请
GET    /api/applications/:id     # 获取申请详情
POST   /api/applications/:id/approve # 审批申请
GET    /api/applications/pending # 获取待审核申请
GET    /api/applications/approved # 获取已审核申请
```

#### 用户管理接口
```
GET    /api/users                # 获取用户列表
POST   /api/users                # 创建用户
GET    /api/users/:id/signature  # 获取用户电子签名
POST   /api/users/:id/signature  # 上传用户电子签名
GET    /api/users/factory-managers # 获取厂长列表
GET    /api/users/managers       # 获取经理列表
```

#### 产品管理接口
```
GET    /api/products             # 获取产品列表
POST   /api/products             # 创建产品
GET    /api/products/:id         # 获取产品详情
PUT    /api/products/:id         # 更新产品
DELETE /api/products/:id         # 删除产品
GET    /api/products/:id/processes # 获取产品工艺流程
```

#### 智能排产接口
```
POST   /api/scheduling/generate  # 生成智能排产方案
GET    /api/scheduling/plans     # 获取排产方案列表
POST   /api/scheduling/optimize  # 优化排产方案
GET    /api/scheduling/analysis  # 获取排产分析数据
```

#### 产能管理接口
```
GET    /api/capacity/equipment   # 获取设备产能配置
POST   /api/capacity/equipment   # 创建设备产能配置
GET    /api/capacity/analysis    # 获取产能分析数据
POST   /api/capacity/calculate   # 计算产能需求
```

#### 设备管理接口
```
GET    /api/equipment            # 获取设备列表（支持筛选、分页、统计）
POST   /api/equipment            # 创建设备
GET    /api/equipment/:id        # 获取设备详情
GET    /api/equipment/factories  # 获取厂区列表
POST   /api/equipment/import     # 批量导入设备
GET    /api/equipment/export     # 导出设备数据
```

#### 质量管理接口
```
GET    /api/quality              # 获取质量报告列表
POST   /api/quality              # 创建质量报告
GET    /api/quality/:id          # 获取质量报告详情
GET    /api/quality/:id/files/:fileId # 下载报告文件
GET    /api/quality/statistics   # 获取质量统计
```

#### 系统监控接口
```
GET    /api/system/health        # 系统健康检查
GET    /api/system/performance   # 获取性能指标
GET    /api/system/logs          # 获取系统日志
GET    /api/system/errors        # 获取错误统计
```

### 部署指南

#### 生产环境部署

1. **环境准备**
   ```bash
   # 安装 PM2 进程管理器
   npm install -g pm2

   # 设置生产环境变量
   export NODE_ENV=production
   ```

2. **启动服务**
   ```bash
   # 使用 PM2 启动
   pm2 start backend/server.js --name "enterprise-app"

   # 设置开机自启
   pm2 startup
   pm2 save
   ```

3. **反向代理配置** (Nginx)
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

#### Docker 部署

```dockerfile
FROM node:14-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3000

CMD ["npm", "start"]
```

## 🔒 安全说明

### 安全特性
- JWT Token 认证，sessionStorage安全存储
- bcrypt 密码加密
- 文件上传类型验证和大小限制
- SQL 注入防护
- XSS 攻击防护
- RBAC权限控制系统
- 电子签名安全管理

### 安全建议
1. **定期更新密码**
2. **使用强密码策略**
3. **定期备份数据**
4. **监控系统日志**
5. **及时更新依赖包**

## 📈 性能优化

### 前端优化
- 组件懒加载
- 图片压缩和优化
- 缓存策略
- 代码分割

### 后端优化
- 数据库索引优化
- 查询性能优化
- 内存缓存
- 文件压缩

### 数据库优化
- 定期清理日志
- 索引维护
- 查询优化
- 备份策略

## 🤝 贡献指南

### 提交代码
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

### 代码规范
- 遵循 ESLint 规则
- 添加适当的注释
- 编写单元测试
- 更新文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- **技术支持**: [<EMAIL>](mailto:<EMAIL>)
- **问题反馈**: [GitHub Issues](https://github.com/Darrowyu/management-system-v2.0.0/issues)
- **项目地址**: [GitHub Repository](https://github.com/Darrowyu/management-system-v2.0.0)
- **功能文档**: [项目功能文档](./func.md)
- **API文档**: [完整API文档](./docs/api/README.md)

---

## 📈 项目统计

- **总代码行数**: 20,000+ 行
- **文件数量**: 100+ 个文件
- **功能模块**: 7个已实现，2个规划中
- **AI算法**: 5种智能优化策略
- **性能提升**: 200%+ 整体性能提升
- **开发周期**: 2025年7月集中开发
- **最新修复**: 电子签名显示问题、权限管理优化

## 🔄 最近更新 (2025-07-29)

### ✅ 已修复问题
- **电子签名显示问题**: 修复了申请书模板中电子签名无法显示的问题
- **认证存储统一**: 统一使用sessionStorage存储认证信息，避免localStorage/sessionStorage不一致
- **路由优化**: 优化了用户路由顺序，确保具体路由优先匹配
- **权限控制**: 完善了电子签名访问权限，所有认证用户都可查看签名

### 🔧 技术改进
- **代码整洁**: 移除了调试代码，提升代码质量
- **错误处理**: 增强了JWT认证错误处理机制
- **性能优化**: 优化了签名获取和显示性能

---

**最后更新**: 2025-07-29
**版本**: v2.0.1
**维护者**: Darrowyu
**开发状态**: 活跃开发中 🚀
