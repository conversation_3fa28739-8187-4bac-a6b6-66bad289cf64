/**
 * 配置文件
 * 集中管理前端配置
 */

import configManager from './configManager.js';

// API基础URL - 从配置管理器动态获取
export const getApiUrl = () => configManager.getApiUrl();

// 兼容性：保留原有的API_URL导出（但建议使用getApiUrl()）
export let API_URL = `${window.location.protocol}//${window.location.hostname}:3000/api`;

// 初始化时更新API_URL
configManager.getApiUrl().then(url => {
    API_URL = url;
}).catch(error => {
    console.warn('配置加载失败，使用默认API_URL:', error);
});

// 部门列表
export const DEPARTMENTS = [
    '生产部',
    '工程部',
    '品管部',
    '机电部',
    '业务部',
    '财务部',
    '人事部',
    '管理部',
    '研发部',
    '采购部',
    '仓储部'
];

// 优先级配置
export const PRIORITIES = {
    normal: {
        label: '普通',
        description: '常规申请，按正常流程处理',
        class: 'bg-green-100 text-green-800'
    },
    medium: {
        label: '中等',
        description: '需要优先处理的申请',
        class: 'bg-yellow-100 text-yellow-800'
    },
    urgent: {
        label: '紧急',
        description: '需要立即处理的紧急申请',
        class: 'bg-red-100 text-red-800'
    }
};

// 文件上传配置
export const UPLOAD_CONFIG = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['.pdf', '.doc', '.docx', '.jpg', '.png'],
    maxFiles: 10
};
