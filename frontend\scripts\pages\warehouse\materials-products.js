/**
 * 仓库管理 - 物料成品管理页面
 * 统一管理物料和成品的入库、出库、退料操作
 */

import { createStandardApp } from '../../common/pageInit.js';
import { 
    getMaterials, createMaterial, updateMaterial, deleteMaterial,
    materialInbound, materialOutbound, materialReturn,
    getProducts, createProduct, updateProduct, deleteProduct,
    productInbound, productOutbound, productReturn,
    validateQRCode, getQRCodeInfo, getStockStatus
} from '../../api/warehouse.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    requiredPermissions: ['warehouse_view'], // 需要仓库查看权限
    components: {
        Sidebar
    },
    setup() {
        const { ref, reactive, onMounted, watch, computed } = Vue;

        // 页面状态
        const activeItemType = ref('material'); // 'material' | 'finished_product'
        const activeOperation = ref('list'); // 'list' | 'inbound' | 'outbound' | 'return'
        const isLoading = ref(false);
        const isSubmitting = ref(false);
        const isScanning = ref(false);

        // 权限检查
        const hasPermission = (permission) => {
            const user = window.currentUser;
            return user && user.permissions && user.permissions.includes(permission);
        };

        // 权限计算属性
        const canManage = computed(() => hasPermission('warehouse_manage'));
        const canInbound = computed(() => hasPermission('warehouse_inbound'));
        const canOutbound = computed(() => hasPermission('warehouse_outbound'));
        const canReturn = computed(() => hasPermission('warehouse_return'));

        // 数据状态
        const items = ref([]);
        const selectedItem = ref(null);
        const qrCodeInput = ref('');

        // 搜索和筛选
        const searchTerm = ref('');
        const stockFilter = ref('');
        const currentPage = ref(1);
        const itemsPerPage = ref(20);

        // 模态框状态
        const showItemModal = ref(false);
        const isEditing = ref(false);

        // 表单数据
        const itemForm = reactive({
            id: '',
            code: '',
            name: '',
            specification: '',
            unit: '',
            min_stock: 0,
            max_stock: 0,
            description: ''
        });

        const operationForm = reactive({
            quantity: 1,
            notes: '',
            department: '',
            reason: ''
        });

        // 计算属性
        const filteredItems = computed(() => {
            let filtered = [...items.value];

            // 搜索筛选
            if (searchTerm.value) {
                const term = searchTerm.value.toLowerCase();
                filtered = filtered.filter(item =>
                    (item.code && item.code.toLowerCase().includes(term)) ||
                    (item.name && item.name.toLowerCase().includes(term))
                );
            }

            // 库存状态筛选
            if (stockFilter.value) {
                filtered = filtered.filter(item => {
                    const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
                    return status === stockFilter.value;
                });
            }

            return filtered;
        });

        const paginatedItems = computed(() => {
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filteredItems.value.slice(start, end);
        });

        const totalItems = computed(() => filteredItems.value.length);
        const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage.value));

        // 监听器
        watch(activeItemType, () => {
            loadItems();
            clearOperation();
        });

        watch(activeOperation, () => {
            clearOperation();
        });

        // 页面初始化
        onMounted(() => {
            loadItems();
        });

        // 加载数据
        async function loadItems() {
            try {
                isLoading.value = true;
                let response;
                
                if (activeItemType.value === 'material') {
                    response = await getMaterials();
                } else {
                    response = await getProducts();
                }

                if (response.success) {
                    items.value = response.data || [];
                } else {
                    console.error('加载失败:', response.message);
                    alert('加载失败: ' + response.message);
                }
            } catch (error) {
                console.error('加载失败:', error);
                alert('加载失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoading.value = false;
            }
        }

        // 刷新列表
        function refreshList() {
            loadItems();
        }

        // 分页控制
        function goToPage(page) {
            if (page >= 1 && page <= totalPages.value) {
                currentPage.value = page;
            }
        }

        // 库存状态相关
        function getStockStatusClass(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            return status;
        }

        function getStockStatusText(item) {
            const status = getStockStatus(item.current_stock || 0, item.min_stock || 0, item.max_stock || 0);
            const statusMap = {
                'normal': '正常',
                'low': '偏低',
                'critical': '紧急',
                'out': '缺货'
            };
            return statusMap[status] || '未知';
        }

        // 二维码验证
        async function validateQRCode() {
            if (!qrCodeInput.value.trim()) {
                alert('请输入二维码');
                return;
            }

            try {
                isScanning.value = true;
                const response = await validateQRCode(qrCodeInput.value.trim());
                
                if (response.success) {
                    const qrInfo = response.data;
                    
                    // 根据二维码信息查找对应的物料或成品
                    const item = items.value.find(i => 
                        i.id === qrInfo.item_id || 
                        i.code === qrInfo.item_code
                    );
                    
                    if (item) {
                        selectedItem.value = item;
                        operationForm.quantity = 1;
                        alert('二维码验证成功！');
                    } else {
                        alert('未找到对应的' + (activeItemType.value === 'material' ? '物料' : '成品'));
                    }
                } else {
                    alert('二维码验证失败: ' + response.message);
                }
            } catch (error) {
                console.error('二维码验证失败:', error);
                alert('二维码验证失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isScanning.value = false;
            }
        }

        // 数量控制
        function increaseQuantity() {
            operationForm.quantity = (operationForm.quantity || 0) + 1;
        }

        function decreaseQuantity() {
            if (operationForm.quantity > 1) {
                operationForm.quantity = operationForm.quantity - 1;
            }
        }

        // 清除操作
        function clearOperation() {
            selectedItem.value = null;
            qrCodeInput.value = '';
            Object.assign(operationForm, {
                quantity: 1,
                notes: '',
                department: '',
                reason: ''
            });
        }

        // 入库操作
        async function submitInbound() {
            if (!selectedItem.value || !operationForm.quantity) {
                alert('请选择物料/成品并输入数量');
                return;
            }

            try {
                isSubmitting.value = true;
                
                const data = {
                    item_id: selectedItem.value.id,
                    quantity: operationForm.quantity,
                    notes: operationForm.notes
                };

                let response;
                if (activeItemType.value === 'material') {
                    response = await materialInbound(data);
                } else {
                    response = await productInbound(data);
                }

                if (response.success) {
                    alert('入库成功！');
                    clearOperation();
                    loadItems();
                } else {
                    alert('入库失败: ' + response.message);
                }
            } catch (error) {
                console.error('入库失败:', error);
                alert('入库失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 出库操作
        async function submitOutbound() {
            if (!selectedItem.value || !operationForm.quantity) {
                alert('请选择物料/成品并输入数量');
                return;
            }

            if (operationForm.quantity > selectedItem.value.current_stock) {
                alert('出库数量不能超过当前库存');
                return;
            }

            try {
                isSubmitting.value = true;
                
                const data = {
                    item_id: selectedItem.value.id,
                    quantity: operationForm.quantity,
                    department: operationForm.department,
                    notes: operationForm.notes
                };

                let response;
                if (activeItemType.value === 'material') {
                    response = await materialOutbound(data);
                } else {
                    response = await productOutbound(data);
                }

                if (response.success) {
                    alert((activeItemType.value === 'material' ? '发料' : '出库') + '成功！');
                    clearOperation();
                    loadItems();
                } else {
                    alert((activeItemType.value === 'material' ? '发料' : '出库') + '失败: ' + response.message);
                }
            } catch (error) {
                console.error('出库失败:', error);
                alert('出库失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 退料/返仓操作
        async function submitReturn() {
            if (!selectedItem.value || !operationForm.quantity) {
                alert('请选择物料/成品并输入数量');
                return;
            }

            try {
                isSubmitting.value = true;
                
                const data = {
                    item_id: selectedItem.value.id,
                    quantity: operationForm.quantity,
                    reason: operationForm.reason,
                    notes: operationForm.notes
                };

                let response;
                if (activeItemType.value === 'material') {
                    response = await materialReturn(data);
                } else {
                    response = await productReturn(data);
                }

                if (response.success) {
                    alert((activeItemType.value === 'material' ? '退料' : '返仓') + '成功！');
                    clearOperation();
                    loadItems();
                } else {
                    alert((activeItemType.value === 'material' ? '退料' : '返仓') + '失败: ' + response.message);
                }
            } catch (error) {
                console.error('退料/返仓失败:', error);
                alert('退料/返仓失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 模态框操作
        function openAddModal() {
            resetItemForm();
            isEditing.value = false;
            showItemModal.value = true;
        }

        function editItem(item) {
            resetItemForm();
            Object.keys(itemForm).forEach(key => {
                if (item[key] !== undefined) {
                    itemForm[key] = item[key];
                }
            });
            isEditing.value = true;
            showItemModal.value = true;
        }

        function closeItemModal() {
            showItemModal.value = false;
            resetItemForm();
        }

        function resetItemForm() {
            Object.assign(itemForm, {
                id: '',
                code: '',
                name: '',
                specification: '',
                unit: '',
                min_stock: 0,
                max_stock: 0,
                description: ''
            });
        }

        // 提交表单
        async function submitItemForm() {
            try {
                isSubmitting.value = true;
                
                const data = { ...itemForm };
                delete data.id;

                let response;
                if (isEditing.value) {
                    if (activeItemType.value === 'material') {
                        response = await updateMaterial(itemForm.id, data);
                    } else {
                        response = await updateProduct(itemForm.id, data);
                    }
                } else {
                    if (activeItemType.value === 'material') {
                        response = await createMaterial(data);
                    } else {
                        response = await createProduct(data);
                    }
                }

                if (response.success) {
                    alert((isEditing.value ? '更新' : '创建') + '成功！');
                    closeItemModal();
                    loadItems();
                } else {
                    alert((isEditing.value ? '更新' : '创建') + '失败: ' + response.message);
                }
            } catch (error) {
                console.error('提交失败:', error);
                alert('提交失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        // 删除项目
        async function deleteItem(item) {
            if (!confirm(`确定要删除${activeItemType.value === 'material' ? '物料' : '成品'} "${item.name}" 吗？`)) {
                return;
            }

            try {
                let response;
                if (activeItemType.value === 'material') {
                    response = await deleteMaterial(item.id);
                } else {
                    response = await deleteProduct(item.id);
                }

                if (response.success) {
                    alert('删除成功！');
                    loadItems();
                } else {
                    alert('删除失败: ' + response.message);
                }
            } catch (error) {
                console.error('删除失败:', error);
                alert('删除失败: ' + (error.response?.data?.message || error.message));
            }
        }

        return {
            // 状态
            activeItemType,
            activeOperation,
            isLoading,
            isSubmitting,
            isScanning,
            // 数据
            items,
            selectedItem,
            qrCodeInput,
            filteredItems,
            paginatedItems,
            totalItems,
            totalPages,
            currentPage,
            // 搜索筛选
            searchTerm,
            stockFilter,
            // 模态框
            showItemModal,
            isEditing,
            itemForm,
            operationForm,
            // 方法
            refreshList,
            goToPage,
            getStockStatusClass,
            getStockStatusText,
            validateQRCode,
            increaseQuantity,
            decreaseQuantity,
            clearOperation,
            submitInbound,
            submitOutbound,
            submitReturn,
            openAddModal,
            editItem,
            closeItemModal,
            submitItemForm,
            deleteItem,
            // 权限检查
            hasPermission,
            canManage,
            canInbound,
            canOutbound,
            canReturn
        };
    },
    onUserLoaded: async (user) => {
        console.log('物料成品管理页面加载完成，当前用户:', user.username);
        // 设置全局用户信息，供权限检查使用
        window.currentUser = user;
    }
}).mount('#app');
