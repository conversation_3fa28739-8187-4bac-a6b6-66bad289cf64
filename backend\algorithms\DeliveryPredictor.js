/**
 * 交期预测器
 * 实现基于历史数据的交期预测、风险评估、缓冲时间计算
 */

const logger = require('../utils/logger');

/**
 * 交期预测器类
 * 负责预测订单交期和评估风险
 */
class DeliveryPredictor {
    constructor() {
        // 预测配置参数
        this.config = {
            historicalDataDays: 90,     // 历史数据天数
            confidenceLevel: 0.95,      // 置信水平
            riskThresholds: {           // 风险阈值
                low: 0.2,
                medium: 0.5,
                high: 0.8
            },
            bufferRatios: {             // 缓冲时间比例
                low: 0.05,              // 低风险5%缓冲
                medium: 0.15,           // 中风险15%缓冲
                high: 0.30              // 高风险30%缓冲
            },
            seasonalFactors: {          // 季节性因子
                spring: 1.0,
                summer: 1.1,
                autumn: 0.9,
                winter: 1.2
            }
        };

        // 只在详细日志模式下显示初始化信息
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('交期预测器初始化完成');
        }
    }

    /**
     * 预测订单交期
     * @param {Object} orderData 订单数据
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 交期预测结果
     */
    async predictDeliveryDate(orderData, scheduleData) {
        try {
            logger.info('开始预测订单交期', { 
                orderId: orderData.id,
                productId: orderData.productId
            });

            // 1. 基础交期计算
            const baseDelivery = await this.calculateBaseDelivery(orderData, scheduleData);
            
            // 2. 历史数据分析
            const historicalAnalysis = await this.analyzeHistoricalData(orderData);
            
            // 3. 风险评估
            const riskAssessment = await this.assessDeliveryRisk(orderData, scheduleData);
            
            // 4. 缓冲时间计算
            const bufferTime = await this.calculateBufferTime(riskAssessment, scheduleData);
            
            // 5. 季节性调整
            const seasonalAdjustment = await this.applySeasonalAdjustment(baseDelivery);
            
            // 6. 最终交期预测
            const finalPrediction = await this.calculateFinalPrediction(
                baseDelivery,
                historicalAnalysis,
                riskAssessment,
                bufferTime,
                seasonalAdjustment
            );

            const result = {
                orderId: orderData.id,
                baseDelivery,
                historicalAnalysis,
                riskAssessment,
                bufferTime,
                seasonalAdjustment,
                finalPrediction,
                predictedAt: new Date().toISOString()
            };

            logger.info('订单交期预测完成', { 
                orderId: orderData.id,
                predictedDate: finalPrediction.deliveryDate,
                riskLevel: riskAssessment.level
            });

            return result;

        } catch (error) {
            logger.error('预测订单交期失败', { 
                error: error.message, 
                orderId: orderData.id 
            });
            throw error;
        }
    }

    /**
     * 计算基础交期
     * @param {Object} orderData 订单数据
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 基础交期信息
     */
    async calculateBaseDelivery(orderData, scheduleData) {
        const startDate = new Date(scheduleData.startDate);
        const productionDays = scheduleData.timeRequirements.timeInDays;
        
        // 计算基础完成日期
        const baseCompletionDate = this.addWorkingDays(startDate, productionDays);
        
        // 添加质检和包装时间
        const qcAndPackingDays = Math.ceil(orderData.quantity / 1000); // 假设每1000件需要1天
        const deliveryReadyDate = this.addWorkingDays(baseCompletionDate, qcAndPackingDays);
        
        // 添加物流时间
        const shippingDays = await this.calculateShippingTime(orderData);
        const baseDeliveryDate = this.addWorkingDays(deliveryReadyDate, shippingDays);

        return {
            startDate: startDate.toISOString().split('T')[0],
            productionDays,
            baseCompletionDate: baseCompletionDate.toISOString().split('T')[0],
            qcAndPackingDays,
            deliveryReadyDate: deliveryReadyDate.toISOString().split('T')[0],
            shippingDays,
            baseDeliveryDate: baseDeliveryDate.toISOString().split('T')[0],
            totalDays: this.calculateWorkingDays(startDate, baseDeliveryDate)
        };
    }

    /**
     * 分析历史数据
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 历史数据分析结果
     */
    async analyzeHistoricalData(orderData) {
        // 获取历史订单数据
        const historicalOrders = await this.getHistoricalOrders(orderData);
        
        if (historicalOrders.length === 0) {
            return {
                dataAvailable: false,
                averageDelay: 0,
                delayVariance: 0,
                onTimeRate: 1.0,
                averageLeadTime: 0
            };
        }

        // 计算延期统计
        const delays = historicalOrders.map(order => {
            const planned = new Date(order.plannedDeliveryDate);
            const actual = new Date(order.actualDeliveryDate);
            return this.calculateWorkingDays(planned, actual);
        });

        const averageDelay = delays.reduce((sum, delay) => sum + delay, 0) / delays.length;
        const delayVariance = this.calculateVariance(delays);
        const onTimeOrders = delays.filter(delay => delay <= 0).length;
        const onTimeRate = onTimeOrders / delays.length;

        // 计算平均交期
        const leadTimes = historicalOrders.map(order => {
            const start = new Date(order.startDate);
            const delivery = new Date(order.actualDeliveryDate);
            return this.calculateWorkingDays(start, delivery);
        });
        const averageLeadTime = leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length;

        return {
            dataAvailable: true,
            sampleSize: historicalOrders.length,
            averageDelay,
            delayVariance,
            onTimeRate,
            averageLeadTime,
            delayDistribution: this.calculateDelayDistribution(delays)
        };
    }

    /**
     * 评估交期风险
     * @param {Object} orderData 订单数据
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 风险评估结果
     */
    async assessDeliveryRisk(orderData, scheduleData) {
        const riskFactors = [];
        let totalRiskScore = 0;

        // 1. 产能风险
        const capacityRisk = await this.assessCapacityRisk(orderData, scheduleData);
        riskFactors.push(capacityRisk);
        totalRiskScore += capacityRisk.score * 0.3;

        // 2. 资源风险
        const resourceRisk = await this.assessResourceRisk(scheduleData);
        riskFactors.push(resourceRisk);
        totalRiskScore += resourceRisk.score * 0.25;

        // 3. 供应链风险
        const supplyChainRisk = await this.assessSupplyChainRisk(orderData);
        riskFactors.push(supplyChainRisk);
        totalRiskScore += supplyChainRisk.score * 0.2;

        // 4. 质量风险
        const qualityRisk = await this.assessQualityRisk(orderData);
        riskFactors.push(qualityRisk);
        totalRiskScore += qualityRisk.score * 0.15;

        // 5. 外部风险
        const externalRisk = await this.assessExternalRisk(orderData);
        riskFactors.push(externalRisk);
        totalRiskScore += externalRisk.score * 0.1;

        // 确定风险等级
        let riskLevel;
        if (totalRiskScore <= this.config.riskThresholds.low) {
            riskLevel = 'low';
        } else if (totalRiskScore <= this.config.riskThresholds.medium) {
            riskLevel = 'medium';
        } else {
            riskLevel = 'high';
        }

        return {
            totalScore: totalRiskScore,
            level: riskLevel,
            factors: riskFactors,
            recommendations: this.generateRiskRecommendations(riskFactors)
        };
    }

    /**
     * 计算缓冲时间
     * @param {Object} riskAssessment 风险评估
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 缓冲时间信息
     */
    async calculateBufferTime(riskAssessment, scheduleData) {
        const baseProductionTime = scheduleData.timeRequirements.timeInDays;
        const riskLevel = riskAssessment.level;
        const bufferRatio = this.config.bufferRatios[riskLevel];
        
        // 基础缓冲时间
        const baseBufferDays = Math.ceil(baseProductionTime * bufferRatio);
        
        // 根据具体风险因子调整
        let adjustedBufferDays = baseBufferDays;
        
        riskAssessment.factors.forEach(factor => {
            if (factor.score > 0.7) {
                adjustedBufferDays += Math.ceil(baseProductionTime * 0.05); // 额外5%
            }
        });

        // 最小缓冲时间（至少1天）
        const finalBufferDays = Math.max(1, adjustedBufferDays);

        return {
            baseBufferDays,
            adjustedBufferDays,
            finalBufferDays,
            bufferRatio,
            riskLevel,
            reasoning: this.generateBufferReasoning(riskAssessment, finalBufferDays)
        };
    }

    /**
     * 应用季节性调整
     * @param {Object} baseDelivery 基础交期
     * @returns {Promise<Object>} 季节性调整结果
     */
    async applySeasonalAdjustment(baseDelivery) {
        const deliveryDate = new Date(baseDelivery.baseDeliveryDate);
        const season = this.getSeason(deliveryDate);
        const seasonalFactor = this.config.seasonalFactors[season];
        
        // 计算季节性调整天数
        const adjustmentDays = Math.ceil((seasonalFactor - 1) * baseDelivery.totalDays);
        
        // 应用调整
        const adjustedDate = this.addWorkingDays(deliveryDate, adjustmentDays);

        return {
            season,
            seasonalFactor,
            adjustmentDays,
            originalDate: baseDelivery.baseDeliveryDate,
            adjustedDate: adjustedDate.toISOString().split('T')[0]
        };
    }

    /**
     * 计算最终预测
     * @param {Object} baseDelivery 基础交期
     * @param {Object} historicalAnalysis 历史分析
     * @param {Object} riskAssessment 风险评估
     * @param {Object} bufferTime 缓冲时间
     * @param {Object} seasonalAdjustment 季节性调整
     * @returns {Promise<Object>} 最终预测结果
     */
    async calculateFinalPrediction(baseDelivery, historicalAnalysis, riskAssessment, bufferTime, seasonalAdjustment) {
        let finalDate = new Date(seasonalAdjustment.adjustedDate);
        
        // 添加缓冲时间
        finalDate = this.addWorkingDays(finalDate, bufferTime.finalBufferDays);
        
        // 如果有历史数据，应用历史延期调整
        if (historicalAnalysis.dataAvailable && historicalAnalysis.averageDelay > 0) {
            const historicalAdjustment = Math.ceil(historicalAnalysis.averageDelay * 0.5); // 50%的历史延期
            finalDate = this.addWorkingDays(finalDate, historicalAdjustment);
        }

        // 计算置信区间
        const confidenceInterval = this.calculateConfidenceInterval(
            finalDate,
            riskAssessment,
            historicalAnalysis
        );

        return {
            deliveryDate: finalDate.toISOString().split('T')[0],
            confidenceLevel: this.config.confidenceLevel,
            confidenceInterval,
            totalBufferDays: bufferTime.finalBufferDays,
            riskLevel: riskAssessment.level,
            onTimeProb: this.calculateOnTimeProbability(riskAssessment, historicalAnalysis),
            components: {
                baseDelivery: baseDelivery.baseDeliveryDate,
                seasonalAdjustment: seasonalAdjustment.adjustmentDays,
                bufferTime: bufferTime.finalBufferDays,
                historicalAdjustment: historicalAnalysis.dataAvailable ? 
                    Math.ceil(historicalAnalysis.averageDelay * 0.5) : 0
            }
        };
    }

    /**
     * 添加工作日
     * @param {Date} startDate 开始日期
     * @param {number} days 天数
     * @returns {Date} 结束日期
     */
    addWorkingDays(startDate, days) {
        const result = new Date(startDate);
        let daysToAdd = Math.ceil(days);
        
        while (daysToAdd > 0) {
            result.setDate(result.getDate() + 1);
            
            // 跳过周末
            if (result.getDay() !== 0 && result.getDay() !== 6) {
                daysToAdd--;
            }
        }
        
        return result;
    }

    /**
     * 计算工作日天数
     * @param {Date} startDate 开始日期
     * @param {Date} endDate 结束日期
     * @returns {number} 工作日天数
     */
    calculateWorkingDays(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        let days = 0;
        
        while (start <= end) {
            if (start.getDay() !== 0 && start.getDay() !== 6) {
                days++;
            }
            start.setDate(start.getDate() + 1);
        }
        
        return days;
    }

    /**
     * 获取季节
     * @param {Date} date 日期
     * @returns {string} 季节
     */
    getSeason(date) {
        const month = date.getMonth() + 1;
        
        if (month >= 3 && month <= 5) return 'spring';
        if (month >= 6 && month <= 8) return 'summer';
        if (month >= 9 && month <= 11) return 'autumn';
        return 'winter';
    }

    /**
     * 计算方差
     * @param {Array} values 数值数组
     * @returns {number} 方差
     */
    calculateVariance(values) {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }

    /**
     * 计算延期分布
     * @param {Array} delays 延期数组
     * @returns {Object} 延期分布
     */
    calculateDelayDistribution(delays) {
        const distribution = {
            onTime: 0,      // <= 0天
            slight: 0,      // 1-3天
            moderate: 0,    // 4-7天
            severe: 0       // > 7天
        };

        delays.forEach(delay => {
            if (delay <= 0) distribution.onTime++;
            else if (delay <= 3) distribution.slight++;
            else if (delay <= 7) distribution.moderate++;
            else distribution.severe++;
        });

        const total = delays.length;
        return {
            onTime: distribution.onTime / total,
            slight: distribution.slight / total,
            moderate: distribution.moderate / total,
            severe: distribution.severe / total
        };
    }

    /**
     * 获取历史订单数据
     * @param {Object} orderData 订单数据
     * @returns {Promise<Array>} 历史订单列表
     */
    async getHistoricalOrders(orderData) {
        // 这里应该调用数据库获取历史订单
        // 暂时返回模拟数据
        return [
            {
                id: 'hist1',
                productId: orderData.productId,
                quantity: 1000,
                startDate: '2024-01-15',
                plannedDeliveryDate: '2024-01-25',
                actualDeliveryDate: '2024-01-27'
            },
            {
                id: 'hist2',
                productId: orderData.productId,
                quantity: 800,
                startDate: '2024-02-01',
                plannedDeliveryDate: '2024-02-10',
                actualDeliveryDate: '2024-02-09'
            }
        ];
    }

    /**
     * 评估产能风险
     * @param {Object} orderData 订单数据
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 产能风险评估
     */
    async assessCapacityRisk(orderData, scheduleData) {
        // 简化实现
        const utilizationRate = scheduleData.resourceUtilization?.equipment?.utilization || 0;
        
        let score = 0;
        if (utilizationRate > 0.9) score = 0.8;
        else if (utilizationRate > 0.8) score = 0.5;
        else if (utilizationRate > 0.7) score = 0.3;
        else score = 0.1;

        return {
            type: 'capacity',
            name: '产能风险',
            score,
            description: `设备利用率${(utilizationRate * 100).toFixed(1)}%`,
            impact: score > 0.5 ? 'high' : score > 0.3 ? 'medium' : 'low'
        };
    }

    /**
     * 评估资源风险
     * @param {Object} scheduleData 排程数据
     * @returns {Promise<Object>} 资源风险评估
     */
    async assessResourceRisk(scheduleData) {
        // 简化实现
        const operatorUtilization = scheduleData.resourceUtilization?.operators?.utilization || 0;
        
        let score = 0;
        if (operatorUtilization > 0.85) score = 0.7;
        else if (operatorUtilization > 0.75) score = 0.4;
        else score = 0.2;

        return {
            type: 'resource',
            name: '资源风险',
            score,
            description: `操作员利用率${(operatorUtilization * 100).toFixed(1)}%`,
            impact: score > 0.5 ? 'high' : score > 0.3 ? 'medium' : 'low'
        };
    }

    /**
     * 评估供应链风险
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 供应链风险评估
     */
    async assessSupplyChainRisk(orderData) {
        // 简化实现
        return {
            type: 'supply_chain',
            name: '供应链风险',
            score: 0.2,
            description: '供应链状况良好',
            impact: 'low'
        };
    }

    /**
     * 评估质量风险
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 质量风险评估
     */
    async assessQualityRisk(orderData) {
        // 简化实现
        return {
            type: 'quality',
            name: '质量风险',
            score: 0.15,
            description: '产品质量稳定',
            impact: 'low'
        };
    }

    /**
     * 评估外部风险
     * @param {Object} orderData 订单数据
     * @returns {Promise<Object>} 外部风险评估
     */
    async assessExternalRisk(orderData) {
        // 简化实现
        return {
            type: 'external',
            name: '外部风险',
            score: 0.1,
            description: '外部环境稳定',
            impact: 'low'
        };
    }

    /**
     * 计算物流时间
     * @param {Object} orderData 订单数据
     * @returns {Promise<number>} 物流天数
     */
    async calculateShippingTime(orderData) {
        // 简化实现，根据客户地址计算物流时间
        return 3; // 默认3天
    }

    /**
     * 生成风险建议
     * @param {Array} riskFactors 风险因子
     * @returns {Array} 建议列表
     */
    generateRiskRecommendations(riskFactors) {
        const recommendations = [];
        
        riskFactors.forEach(factor => {
            if (factor.score > 0.5) {
                switch (factor.type) {
                    case 'capacity':
                        recommendations.push('建议增加设备产能或调整生产计划');
                        break;
                    case 'resource':
                        recommendations.push('建议增加操作员或提高技能培训');
                        break;
                    case 'supply_chain':
                        recommendations.push('建议提前备货或寻找备用供应商');
                        break;
                    case 'quality':
                        recommendations.push('建议加强质量控制和检验');
                        break;
                    case 'external':
                        recommendations.push('建议制定应急预案');
                        break;
                }
            }
        });
        
        return recommendations;
    }

    /**
     * 生成缓冲时间说明
     * @param {Object} riskAssessment 风险评估
     * @param {number} bufferDays 缓冲天数
     * @returns {string} 说明文本
     */
    generateBufferReasoning(riskAssessment, bufferDays) {
        return `基于${riskAssessment.level}风险等级，建议${bufferDays}天缓冲时间`;
    }

    /**
     * 计算置信区间
     * @param {Date} predictedDate 预测日期
     * @param {Object} riskAssessment 风险评估
     * @param {Object} historicalAnalysis 历史分析
     * @returns {Object} 置信区间
     */
    calculateConfidenceInterval(predictedDate, riskAssessment, historicalAnalysis) {
        const variance = historicalAnalysis.delayVariance || 4; // 默认方差
        const standardDeviation = Math.sqrt(variance);
        const marginOfError = 1.96 * standardDeviation; // 95%置信水平
        
        const lowerBound = this.addWorkingDays(predictedDate, -Math.ceil(marginOfError));
        const upperBound = this.addWorkingDays(predictedDate, Math.ceil(marginOfError));
        
        return {
            lower: lowerBound.toISOString().split('T')[0],
            upper: upperBound.toISOString().split('T')[0],
            marginOfError: Math.ceil(marginOfError)
        };
    }

    /**
     * 计算按时交付概率
     * @param {Object} riskAssessment 风险评估
     * @param {Object} historicalAnalysis 历史分析
     * @returns {number} 按时交付概率
     */
    calculateOnTimeProbability(riskAssessment, historicalAnalysis) {
        let baseProbability = 0.9; // 基础概率90%
        
        // 根据风险等级调整
        switch (riskAssessment.level) {
            case 'low':
                baseProbability = 0.95;
                break;
            case 'medium':
                baseProbability = 0.85;
                break;
            case 'high':
                baseProbability = 0.70;
                break;
        }
        
        // 根据历史数据调整
        if (historicalAnalysis.dataAvailable) {
            const historicalOnTimeRate = historicalAnalysis.onTimeRate;
            baseProbability = (baseProbability + historicalOnTimeRate) / 2;
        }
        
        return Math.max(0.1, Math.min(0.99, baseProbability));
    }
}

module.exports = DeliveryPredictor;
