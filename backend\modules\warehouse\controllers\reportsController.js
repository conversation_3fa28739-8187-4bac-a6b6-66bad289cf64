/**
 * 仓库报表控制器
 * 处理仓库报表相关的HTTP请求
 */

const MaterialsService = require('../services/materialsService');
const ProductsService = require('../services/productsService');
const TransactionsService = require('../services/transactionsService');
const QRCodesService = require('../services/qrcodesService');
const logger = require('../../../utils/logger');

class ReportsController {
    /**
     * 获取库存报告
     */
    async getInventoryReport(req, res) {
        try {
            const filters = {
                stock_status: req.query.stock_status,
                item_type: req.query.item_type
            };

            let materialsInventory = [];
            let productsInventory = [];

            if (!filters.item_type || filters.item_type === 'material') {
                materialsInventory = await MaterialsService.getMaterialsInventory(filters);
            }

            if (!filters.item_type || filters.item_type === 'product') {
                productsInventory = await ProductsService.getProductsInventory(filters);
            }

            // 统计汇总
            const summary = {
                total_materials: materialsInventory.length,
                total_products: productsInventory.length,
                low_stock_materials: materialsInventory.filter(m => m.stock_status === 'low_stock').length,
                low_stock_products: productsInventory.filter(p => p.stock_status === 'low_stock').length,
                high_stock_materials: materialsInventory.filter(m => m.stock_status === 'high_stock').length,
                high_stock_products: productsInventory.filter(p => p.stock_status === 'high_stock').length
            };

            res.json({
                success: true,
                data: {
                    summary,
                    materials: materialsInventory,
                    products: productsInventory
                },
                message: '获取库存报告成功'
            });
        } catch (error) {
            logger.error('获取库存报告失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取库存报告失败'
            });
        }
    }

    /**
     * 获取事务报告
     */
    async getTransactionReport(req, res) {
        try {
            const filters = {
                start_date: req.query.start_date,
                end_date: req.query.end_date,
                item_type: req.query.item_type,
                transaction_type: req.query.transaction_type
            };

            // 获取事务列表
            const transactions = await TransactionsService.getTransactions(filters);
            
            // 获取统计数据
            const stats = await TransactionsService.getTransactionStats(filters);

            // 按类型汇总
            const summary = {
                total_transactions: transactions.length,
                inbound_count: transactions.filter(t => t.transaction_type === 'inbound').length,
                outbound_count: transactions.filter(t => t.transaction_type === 'outbound').length,
                return_count: transactions.filter(t => t.transaction_type === 'return').length,
                total_inbound_quantity: transactions
                    .filter(t => t.transaction_type === 'inbound')
                    .reduce((sum, t) => sum + t.quantity, 0),
                total_outbound_quantity: transactions
                    .filter(t => t.transaction_type === 'outbound')
                    .reduce((sum, t) => sum + t.quantity, 0),
                total_return_quantity: transactions
                    .filter(t => t.transaction_type === 'return')
                    .reduce((sum, t) => sum + t.quantity, 0)
            };

            res.json({
                success: true,
                data: {
                    summary,
                    transactions,
                    stats
                },
                message: '获取事务报告成功'
            });
        } catch (error) {
            logger.error('获取事务报告失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取事务报告失败'
            });
        }
    }

    /**
     * 获取库存预警
     */
    async getInventoryAlerts(req, res) {
        try {
            // 获取低库存物料
            const lowStockMaterials = await MaterialsService.getMaterialsInventory({ stock_status: 'low_stock' });
            
            // 获取低库存成品
            const lowStockProducts = await ProductsService.getProductsInventory({ stock_status: 'low_stock' });
            
            // 获取高库存物料
            const highStockMaterials = await MaterialsService.getMaterialsInventory({ stock_status: 'high_stock' });
            
            // 获取高库存成品
            const highStockProducts = await ProductsService.getProductsInventory({ stock_status: 'high_stock' });

            const alerts = [
                ...lowStockMaterials.map(m => ({
                    type: 'low_stock',
                    item_type: 'material',
                    item_id: m.id,
                    item_code: m.material_code,
                    item_name: m.material_name,
                    current_stock: m.current_stock,
                    min_stock_level: m.min_stock_level,
                    max_stock_level: m.max_stock_level,
                    alert_level: 'warning',
                    message: `物料 ${m.material_name} 库存不足，当前库存: ${m.current_stock}，最低库存: ${m.min_stock_level}`
                })),
                ...lowStockProducts.map(p => ({
                    type: 'low_stock',
                    item_type: 'product',
                    item_id: p.id,
                    item_code: p.product_code,
                    item_name: p.product_name,
                    current_stock: p.current_stock,
                    min_stock_level: p.min_stock_level,
                    max_stock_level: p.max_stock_level,
                    alert_level: 'warning',
                    message: `成品 ${p.product_name} 库存不足，当前库存: ${p.current_stock}，最低库存: ${p.min_stock_level}`
                })),
                ...highStockMaterials.map(m => ({
                    type: 'high_stock',
                    item_type: 'material',
                    item_id: m.id,
                    item_code: m.material_code,
                    item_name: m.material_name,
                    current_stock: m.current_stock,
                    min_stock_level: m.min_stock_level,
                    max_stock_level: m.max_stock_level,
                    alert_level: 'info',
                    message: `物料 ${m.material_name} 库存过高，当前库存: ${m.current_stock}，最高库存: ${m.max_stock_level}`
                })),
                ...highStockProducts.map(p => ({
                    type: 'high_stock',
                    item_type: 'product',
                    item_id: p.id,
                    item_code: p.product_code,
                    item_name: p.product_name,
                    current_stock: p.current_stock,
                    min_stock_level: p.min_stock_level,
                    max_stock_level: p.max_stock_level,
                    alert_level: 'info',
                    message: `成品 ${p.product_name} 库存过高，当前库存: ${p.current_stock}，最高库存: ${p.max_stock_level}`
                }))
            ];

            const summary = {
                total_alerts: alerts.length,
                low_stock_alerts: alerts.filter(a => a.type === 'low_stock').length,
                high_stock_alerts: alerts.filter(a => a.type === 'high_stock').length,
                material_alerts: alerts.filter(a => a.item_type === 'material').length,
                product_alerts: alerts.filter(a => a.item_type === 'product').length
            };

            res.json({
                success: true,
                data: {
                    summary,
                    alerts
                },
                message: '获取库存预警成功'
            });
        } catch (error) {
            logger.error('获取库存预警失败:', error);
            res.status(500).json({
                success: false,
                message: error.message || '获取库存预警失败'
            });
        }
    }

    /**
     * 获取追溯报告
     */
    async getTraceabilityReport(req, res) {
        try {
            const { qrcode } = req.params;

            if (!qrcode) {
                return res.status(400).json({
                    success: false,
                    message: '缺少二维码参数'
                });
            }

            // 获取二维码信息
            const qrcodeInfo = await QRCodesService.getQRCodeInfo(qrcode);
            
            // 获取相关的所有事务
            const transactions = await TransactionsService.getTransactionsByItem(
                qrcodeInfo.item_type, 
                qrcodeInfo.item_id
            );

            // 获取使用该二维码的事务
            const qrcodeTransactions = transactions.filter(t => t.qrcode === qrcode);

            // 构建追溯链
            const traceabilityChain = transactions.map(t => ({
                transaction_id: t.transaction_id,
                transaction_type: t.transaction_type,
                quantity: t.quantity,
                batch_number: t.batch_number,
                qrcode: t.qrcode,
                operator_name: t.operator_name,
                transaction_date: t.transaction_date,
                notes: t.notes,
                is_qrcode_related: t.qrcode === qrcode
            })).sort((a, b) => new Date(a.transaction_date) - new Date(b.transaction_date));

            res.json({
                success: true,
                data: {
                    qrcode_info: qrcodeInfo,
                    traceability_chain: traceabilityChain,
                    qrcode_transactions: qrcodeTransactions,
                    summary: {
                        total_transactions: traceabilityChain.length,
                        qrcode_related_transactions: qrcodeTransactions.length,
                        first_transaction_date: traceabilityChain.length > 0 ? traceabilityChain[0].transaction_date : null,
                        last_transaction_date: traceabilityChain.length > 0 ? traceabilityChain[traceabilityChain.length - 1].transaction_date : null
                    }
                },
                message: '获取追溯报告成功'
            });
        } catch (error) {
            logger.error('获取追溯报告失败:', error);
            res.status(error.message.includes('不存在') ? 404 : 500).json({
                success: false,
                message: error.message || '获取追溯报告失败'
            });
        }
    }
}

module.exports = new ReportsController();
