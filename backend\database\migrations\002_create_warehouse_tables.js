/**
 * 仓库管理系统数据表创建迁移
 * 创建物料表、成品表、库存事务表、二维码管理表
 */

const logger = require('../../utils/logger');

class WarehouseTablesMigration {
    constructor(db) {
        this.db = db;
    }

    async migrate() {
        try {
            logger.info('开始创建仓库管理系统数据表...');

            // 1. 创建物料表 (warehouse_materials)
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_materials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    material_code VARCHAR(50) UNIQUE NOT NULL,     -- 物料编码
                    material_name VARCHAR(200) NOT NULL,           -- 物料名称
                    material_type VARCHAR(50) NOT NULL,            -- 物料类型(常规/客供)
                    supplier_code VARCHAR(50),                     -- 供应商代码
                    supplier_name VARCHAR(200),                    -- 供应商名称
                    unit VARCHAR(20) NOT NULL,                     -- 单位
                    safety_stock INTEGER DEFAULT 0,               -- 安全库存
                    current_stock INTEGER DEFAULT 0,              -- 当前库存
                    status VARCHAR(20) DEFAULT 'active',          -- 状态
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // 2. 创建成品表 (warehouse_finished_products)
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_finished_products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_code VARCHAR(50) UNIQUE NOT NULL,      -- 成品编码
                    product_name VARCHAR(200) NOT NULL,            -- 成品名称
                    batch_number VARCHAR(50),                      -- 批次号
                    boxes_per_unit INTEGER DEFAULT 1,             -- 每箱盒数
                    pieces_per_box INTEGER DEFAULT 1,             -- 每盒件数
                    total_pieces INTEGER,                          -- 总件数
                    current_stock INTEGER DEFAULT 0,              -- 当前库存
                    production_date DATE,                          -- 生产日期
                    expiry_date DATE,                             -- 有效期
                    status VARCHAR(20) DEFAULT 'active',          -- 状态
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // 3. 创建库存事务表 (warehouse_inventory_transactions)
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_inventory_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id VARCHAR(50) UNIQUE NOT NULL,    -- 事务编号
                    item_type VARCHAR(20) NOT NULL,               -- 物品类型(material/product)
                    item_id INTEGER NOT NULL,                     -- 物品ID
                    transaction_type VARCHAR(20) NOT NULL,        -- 事务类型(inbound/outbound/return)
                    quantity INTEGER NOT NULL,                    -- 数量
                    unit VARCHAR(20) NOT NULL,                    -- 单位
                    qrcode VARCHAR(100),                          -- 二维码
                    reason VARCHAR(100),                          -- 原因
                    operator_id INTEGER NOT NULL,                -- 操作员ID
                    order_number VARCHAR(50),                     -- 订单号
                    supplier_info TEXT,                           -- 供应商信息
                    customer_info TEXT,                           -- 客户信息
                    notes TEXT,                                   -- 备注
                    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (operator_id) REFERENCES users(id)
                );
            `);

            // 4. 创建二维码管理表 (warehouse_qrcodes)
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    qrcode VARCHAR(100) UNIQUE NOT NULL,          -- 二维码
                    item_type VARCHAR(20) NOT NULL,               -- 物品类型
                    item_id INTEGER NOT NULL,                     -- 物品ID
                    batch_info VARCHAR(100),                      -- 批次信息
                    quantity INTEGER,                             -- 数量
                    status VARCHAR(20) DEFAULT 'active',          -- 状态
                    generated_by INTEGER NOT NULL,               -- 生成人
                    used_by INTEGER,                             -- 使用人
                    used_at DATETIME,                            -- 使用时间
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (generated_by) REFERENCES users(id),
                    FOREIGN KEY (used_by) REFERENCES users(id)
                );
            `);

            // 创建索引以提高查询性能
            this.db.exec(`
                CREATE INDEX IF NOT EXISTS idx_warehouse_materials_code ON warehouse_materials(material_code);
                CREATE INDEX IF NOT EXISTS idx_warehouse_materials_type ON warehouse_materials(material_type);
                CREATE INDEX IF NOT EXISTS idx_warehouse_materials_status ON warehouse_materials(status);

                CREATE INDEX IF NOT EXISTS idx_warehouse_products_code ON warehouse_finished_products(product_code);
                CREATE INDEX IF NOT EXISTS idx_warehouse_products_batch ON warehouse_finished_products(batch_number);
                CREATE INDEX IF NOT EXISTS idx_warehouse_products_status ON warehouse_finished_products(status);

                CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_id ON warehouse_inventory_transactions(transaction_id);
                CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_type ON warehouse_inventory_transactions(transaction_type);
                CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_item ON warehouse_inventory_transactions(item_type, item_id);
                CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_date ON warehouse_inventory_transactions(transaction_date);
                CREATE INDEX IF NOT EXISTS idx_warehouse_transactions_operator ON warehouse_inventory_transactions(operator_id);

                CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_code ON warehouse_qrcodes(qrcode);
                CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_item ON warehouse_qrcodes(item_type, item_id);
                CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_status ON warehouse_qrcodes(status);
            `);

            logger.info('仓库管理系统数据表创建成功');
            return true;
        } catch (error) {
            logger.error('创建仓库管理系统数据表失败:', error);
            throw error;
        }
    }
}

module.exports = WarehouseTablesMigration;
