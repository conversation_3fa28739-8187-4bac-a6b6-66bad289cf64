/**
 * 二维码管理服务层
 * 处理二维码生成、验证和管理
 */

const databaseManager = require('../../../database/database');
const logger = require('../../../utils/logger');
const crypto = require('crypto');

class QRCodesService {
    constructor() {
        this.db = databaseManager.getConnection();
    }

    /**
     * 生成二维码
     */
    async generateQRCode(qrcodeData, operatorId) {
        try {
            const { item_type, item_id, batch_number, expiry_date, notes } = qrcodeData;

            // 验证物品存在
            await this.validateItem(item_type, item_id);

            // 生成唯一二维码
            const qrcode = this.generateUniqueQRCode(item_type, item_id, batch_number);

            // 检查二维码是否已存在
            const existing = this.db.prepare('SELECT id FROM warehouse_qrcodes WHERE qrcode = ?').get(qrcode);
            if (existing) {
                throw new Error('二维码已存在，请重新生成');
            }

            const result = this.db.prepare(`
                INSERT INTO warehouse_qrcodes (
                    qrcode, item_type, item_id, batch_number, 
                    expiry_date, notes, status, created_by, 
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, datetime('now'), datetime('now'))
            `).run(qrcode, item_type, item_id, batch_number, expiry_date, notes, operatorId);

            logger.info(`二维码生成成功: ${qrcode}`, { 
                item_type, 
                item_id, 
                batch_number, 
                operatorId 
            });

            return {
                id: result.lastInsertRowid,
                qrcode,
                item_type,
                item_id,
                batch_number,
                status: 'active'
            };
        } catch (error) {
            logger.error('生成二维码失败:', error);
            throw error;
        }
    }

    /**
     * 验证二维码
     */
    async validateQRCode(qrcode, expectedItemType = null, expectedItemId = null) {
        try {
            const qrcodeInfo = this.db.prepare(`
                SELECT q.*, 
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE q.qrcode = ?
            `).get(qrcode);

            if (!qrcodeInfo) {
                throw new Error('二维码不存在');
            }

            if (qrcodeInfo.status !== 'active') {
                throw new Error(`二维码状态异常: ${qrcodeInfo.status}`);
            }

            // 检查过期时间
            if (qrcodeInfo.expiry_date) {
                const expiryDate = new Date(qrcodeInfo.expiry_date);
                const now = new Date();
                if (now > expiryDate) {
                    throw new Error('二维码已过期');
                }
            }

            // 验证物品类型和ID（如果指定）
            if (expectedItemType && qrcodeInfo.item_type !== expectedItemType) {
                throw new Error(`二维码物品类型不匹配，期望: ${expectedItemType}, 实际: ${qrcodeInfo.item_type}`);
            }

            if (expectedItemId && qrcodeInfo.item_id !== expectedItemId) {
                throw new Error(`二维码物品ID不匹配，期望: ${expectedItemId}, 实际: ${qrcodeInfo.item_id}`);
            }

            return qrcodeInfo;
        } catch (error) {
            logger.error('验证二维码失败:', error);
            throw error;
        }
    }

    /**
     * 获取二维码信息
     */
    async getQRCodeInfo(qrcode) {
        try {
            const qrcodeInfo = this.db.prepare(`
                SELECT q.*, 
                       u.username as created_by_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN users u ON q.created_by = u.id
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE q.qrcode = ?
            `).get(qrcode);

            if (!qrcodeInfo) {
                throw new Error('二维码不存在');
            }

            // 获取使用历史
            const usageHistory = this.db.prepare(`
                SELECT t.*, u.username as operator_name
                FROM warehouse_inventory_transactions t
                LEFT JOIN users u ON t.operator_id = u.id
                WHERE t.qrcode = ?
                ORDER BY t.transaction_date DESC
            `).all(qrcode);

            return {
                ...qrcodeInfo,
                usage_history: usageHistory
            };
        } catch (error) {
            logger.error('获取二维码信息失败:', error);
            throw error;
        }
    }

    /**
     * 使用二维码（标记为已使用）
     */
    async useQRCode(id, operatorId, notes = null) {
        try {
            const qrcodeInfo = this.db.prepare('SELECT * FROM warehouse_qrcodes WHERE id = ?').get(id);
            
            if (!qrcodeInfo) {
                throw new Error('二维码不存在');
            }

            if (qrcodeInfo.status !== 'active') {
                throw new Error(`二维码状态异常: ${qrcodeInfo.status}`);
            }

            const result = this.db.prepare(`
                UPDATE warehouse_qrcodes 
                SET status = 'used', used_by = ?, used_at = datetime('now'), 
                    notes = CASE WHEN ? IS NOT NULL THEN ? ELSE notes END,
                    updated_at = datetime('now')
                WHERE id = ?
            `).run(operatorId, notes, notes, id);

            if (result.changes === 0) {
                throw new Error('更新二维码状态失败');
            }

            logger.info(`二维码使用成功: ${qrcodeInfo.qrcode}`, { operatorId });

            return {
                id,
                qrcode: qrcodeInfo.qrcode,
                status: 'used',
                used_by: operatorId,
                used_at: new Date().toISOString()
            };
        } catch (error) {
            logger.error('使用二维码失败:', error);
            throw error;
        }
    }

    /**
     * 批量生成二维码
     */
    async batchGenerateQRCodes(batchData, operatorId) {
        try {
            const { item_type, item_id, batch_number, quantity, expiry_date, notes } = batchData;

            // 验证物品存在
            await this.validateItem(item_type, item_id);

            const qrcodes = [];
            const transaction = this.db.transaction(() => {
                for (let i = 1; i <= quantity; i++) {
                    const qrcode = this.generateUniqueQRCode(item_type, item_id, batch_number, i);
                    
                    const result = this.db.prepare(`
                        INSERT INTO warehouse_qrcodes (
                            qrcode, item_type, item_id, batch_number, 
                            expiry_date, notes, status, created_by, 
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, datetime('now'), datetime('now'))
                    `).run(qrcode, item_type, item_id, batch_number, expiry_date, notes, operatorId);

                    qrcodes.push({
                        id: result.lastInsertRowid,
                        qrcode,
                        item_type,
                        item_id,
                        batch_number
                    });
                }
            });

            transaction();

            logger.info(`批量生成二维码成功: ${quantity}个`, { 
                item_type, 
                item_id, 
                batch_number, 
                operatorId 
            });

            return qrcodes;
        } catch (error) {
            logger.error('批量生成二维码失败:', error);
            throw error;
        }
    }

    /**
     * 生成唯一二维码
     */
    generateUniqueQRCode(itemType, itemId, batchNumber = null, sequence = null) {
        const timestamp = Date.now().toString(36); // 时间戳转36进制
        const random = crypto.randomBytes(4).toString('hex').toUpperCase(); // 8位随机字符
        
        let qrcode = `${itemType.toUpperCase()}-${itemId}-${timestamp}-${random}`;
        
        if (batchNumber) {
            qrcode += `-B${batchNumber}`;
        }
        
        if (sequence) {
            qrcode += `-S${sequence.toString().padStart(3, '0')}`;
        }
        
        return qrcode;
    }

    /**
     * 验证物品是否存在
     */
    async validateItem(itemType, itemId) {
        try {
            let exists = false;
            
            if (itemType === 'material') {
                const material = this.db.prepare('SELECT id FROM warehouse_materials WHERE id = ? AND status = ?').get(itemId, 'active');
                exists = !!material;
            } else if (itemType === 'product') {
                const product = this.db.prepare('SELECT id FROM warehouse_finished_products WHERE id = ? AND status = ?').get(itemId, 'active');
                exists = !!product;
            }
            
            if (!exists) {
                throw new Error(`${itemType === 'material' ? '物料' : '成品'}不存在或状态异常`);
            }
            
            return true;
        } catch (error) {
            logger.error('验证物品失败:', error);
            throw error;
        }
    }

    /**
     * 获取二维码列表
     */
    async getQRCodes(filters = {}) {
        try {
            let query = `
                SELECT q.*, 
                       u.username as created_by_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_name
                           WHEN q.item_type = 'product' THEN p.product_name
                           ELSE 'Unknown'
                       END as item_name,
                       CASE 
                           WHEN q.item_type = 'material' THEN m.material_code
                           WHEN q.item_type = 'product' THEN p.product_code
                           ELSE 'Unknown'
                       END as item_code
                FROM warehouse_qrcodes q
                LEFT JOIN users u ON q.created_by = u.id
                LEFT JOIN warehouse_materials m ON q.item_type = 'material' AND q.item_id = m.id
                LEFT JOIN warehouse_finished_products p ON q.item_type = 'product' AND q.item_id = p.id
                WHERE 1=1
            `;

            const params = [];

            if (filters.item_type) {
                query += ' AND q.item_type = ?';
                params.push(filters.item_type);
            }

            if (filters.item_id) {
                query += ' AND q.item_id = ?';
                params.push(filters.item_id);
            }

            if (filters.status) {
                query += ' AND q.status = ?';
                params.push(filters.status);
            }

            if (filters.batch_number) {
                query += ' AND q.batch_number = ?';
                params.push(filters.batch_number);
            }

            query += ' ORDER BY q.created_at DESC';

            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }

            return this.db.prepare(query).all(params);
        } catch (error) {
            logger.error('获取二维码列表失败:', error);
            throw error;
        }
    }
}

module.exports = new QRCodesService();
